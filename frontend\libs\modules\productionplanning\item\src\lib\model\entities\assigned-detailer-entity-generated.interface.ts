/*
 * $Id$
 * Copyright(c) RIB Software GmbH
 */

import { IPPSItem2ClerkEntity } from '@libs/productionplanning/common';

export interface IAssignedDetailerEntityGenerated {

/*
 * Detailers
 */
  Detailers?: IPPSItem2ClerkEntity[] | null;

/*
 * IsLogEnable
 */
  IsLogEnable?: boolean | null;

/*
 * UpdateReason
 */
  UpdateReason?: number | null;

/*
 * UpdateRemark
 */
  UpdateRemark?: string | null;
}
