/*
 * Copyright(c) RIB Software GmbH
 */

import { Injectable } from '@angular/core';
import { IEntityContainerBehavior, IGridContainerLink } from '@libs/ui/business-base';
import { ITimeSheetEntity } from '../model/models';
import { ItemType } from '@libs/ui/common';

@Injectable({
	providedIn: 'root',
})
export class ProductionplanningReportTimeSheetBehavior implements IEntityContainerBehavior<IGridContainerLink<ITimeSheetEntity>, ITimeSheetEntity> {
	public onCreate(containerLink: IGridContainerLink<ITimeSheetEntity>): void {
		containerLink.uiAddOns.toolbar.addItems([
			{
				id: 't1',
				caption: { key: 'productionplanning.report.timesheet.AutoCreate' },
				hideItem: false,
				iconClass: 'tlb-icons ico-timesheet-autocreate',
				fn: () => {
					throw new Error('This method is not implemented');
				},
				disabled: false,
				sort: 1,
				type: ItemType.Item,
			},
		]);
	}
}
