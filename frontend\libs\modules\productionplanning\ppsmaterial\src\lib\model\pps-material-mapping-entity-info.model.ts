/*
 * Copyright(c) RIB Software GmbH
 */

import { EntityInfo } from '@libs/ui/business-base';
import { IPpsMaterialMappingEntity } from './models';
import { ILayoutConfiguration, ILookupContext } from '@libs/ui/common';
import { prefixAllTranslationKeys } from '@libs/platform/common';
import { PpsMaterialMappingDataService } from '../services/mapping/pps-material-mapping-data.service';
import { BASICS_CUSTOMIZE_LOOKUP_PROVIDER_TOKEN, IBasicsCustomizeExternalSourceEntity } from '@libs/basics/interfaces';

export const PPS_MATERIAL_MAPPING_ENTITY_INFO: EntityInfo = EntityInfo.create<IPpsMaterialMappingEntity>({
	grid: {
		title: { key: 'productionplanning.ppsmaterial.mapping.listViewTitle' },
		containerUuid: '3f4a022fd7a24394892e4666b5d24240',
	},
	//Don't know why this container is without permission uuid in js
	permissionUuid: '3f4a022fd7a24394892e4666b5d24240',
	dataService: (ctx) => ctx.injector.get(PpsMaterialMappingDataService),
	dtoSchemeId: { moduleSubModule: 'ProductionPlanning.PpsMaterial', typeName: 'PpsMaterialMappingDto' },
	layoutConfiguration: async (context) => {
		const customizeLookupProvider = await context.lazyInjector.inject(BASICS_CUSTOMIZE_LOOKUP_PROVIDER_TOKEN);
		return <ILayoutConfiguration<IPpsMaterialMappingEntity>>{
			groups: [
				{
					gid: 'basicData',
					title: {
						key: 'cloud.common.listProductDescTitle',
						text: 'Basic Data',
					},
					attributes: ['Description', 'MappingCode', 'BasExternalsourcetypeFk', 'BasExternalsourceFk'],
				},
				{
					gid: 'userDefTextGroup',
					title: {
						key: 'cloud.common.UserdefTexts',
						text: 'User-Defined Texts',
					},
					attributes: ['Userdefined1', 'Userdefined2', 'Userdefined3', 'Userdefined4', 'Userdefined5'],
				},
			],
			overloads: {
				BasExternalsourcetypeFk: customizeLookupProvider.provideExternalSourceTypeLookupOverload({ showClearButton: false }),
				BasExternalsourceFk: customizeLookupProvider.provideExternalSourceLookupOverload({
					showClearButton: true,
					clientSideFilter: {
						execute(item: IBasicsCustomizeExternalSourceEntity, context: ILookupContext<IBasicsCustomizeExternalSourceEntity, IPpsMaterialMappingEntity>): boolean {
							if (context.entity?.BasExternalsourcetypeFk) {
								if (item.ExternalsourcetypeFk) {
									return item.ExternalsourcetypeFk === context.entity.BasExternalsourcetypeFk;
								}
								return false;
							}
							return false;
						},
					},
				}),
			},
			labels: {
				...prefixAllTranslationKeys('cloud.common.', {
					Description: { key: 'entityDescription', text: 'Description' },
					Userdefined1: {
						key: 'entityUserDefText',
						params: { p_0: '1' },
					},
					Userdefined2: {
						key: 'entityUserDefText',
						params: { p_0: '2' },
					},
					Userdefined3: {
						key: 'entityUserDefText',
						params: { p_0: '3' },
					},
					Userdefined4: {
						key: 'entityUserDefText',
						params: { p_0: '4' },
					},
					Userdefined5: {
						key: 'entityUserDefText',
						params: { p_0: '5' },
					},
				}),
				...prefixAllTranslationKeys('productionplanning.ppsmaterial.', {
					MappingCode: { key: 'mapping.mappingCode', text: '*Code' },
					BasExternalsourcetypeFk: { key: 'mapping.basexternalsourcetypefk', text: '*External Source Type' },
					BasExternalsourceFk: { key: 'mapping.basexternalsourcefk', text: '*External Source' },
				}),
			},
		};
	},
});
