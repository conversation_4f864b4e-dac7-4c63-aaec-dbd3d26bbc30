/*
 * Copyright(c) RIB Software GmbH
 */

import { Injectable } from '@angular/core';

import { DataServiceFlatLeaf, IDataServiceOptions, ServiceRole, IDataServiceChildRoleOptions, IDataServiceEndPointOptions, EntityArrayProcessor } from '@libs/platform/data-access';
import { IReportEntity, ITimeSheetEntity, ReportComplete } from '../model/models';
import { ProductionplanningReportReportDataService } from './productionplanning-report-report-data.service';
import { IIdentificationData } from '@libs/platform/common';
import { get } from 'lodash';
import { PpsReportTimesheetProcessorService } from './processors/pps-report-timesheet-processor.service';

@Injectable({
	providedIn: 'root',
})
export class ProductionplanningReportTimeSheetDataService extends DataServiceFlatLeaf<ITimeSheetEntity, IReportEntity, ReportComplete> {
	public  readonlyProcessor = new PpsReportTimesheetProcessorService();
	public constructor(parentDataService: ProductionplanningReportReportDataService) {
		const options: IDataServiceOptions<ITimeSheetEntity> = {
			apiUrl: 'productionplanning/report/timesheet',
			readInfo: <IDataServiceEndPointOptions>{
				endPoint: 'list',
				prepareParam: (ident: IIdentificationData) => {
					return { ReportFk: ident.pKey1 };
				},
				usePost: false,
			},
			createInfo: <IDataServiceEndPointOptions>{
				endPoint: 'create',
				usePost: true,
				prepareParam: ident => {
					return { Pkey1: ident.pKey1 };
				}
			},
			updateInfo: <IDataServiceEndPointOptions>{
				endPoint: 'update',
			},
			roleInfo: <IDataServiceChildRoleOptions<ITimeSheetEntity, IReportEntity, ReportComplete>>{
				role: ServiceRole.Leaf,
				itemName: 'TimeSheet',
				parent: parentDataService,
				parentFilter: 'ReportFk',
			},
			processors: [new EntityArrayProcessor<ITimeSheetEntity>([])],
		};

		super(options);
		this.readonlyProcessor.setDataService(this);
		this.processor.addProcessor(this.readonlyProcessor);



	}

	/**
	 * Indicates that this service should be registered by method.
	 * @returns {boolean} Always returns true.
	 */
	public override registerByMethod(): boolean {
		return true;
	}

	/**
	 * Registers modifications (added/updated/deleted) to the parent update object.
	 * @param parentUpdate The parent ReportComplete object to update.
	 * @param modified Array of modified ITimeSheetEntity objects.
	 * @param deleted Array of deleted ITimeSheetEntity objects.
	 */
	public override registerModificationsToParentUpdate(parentUpdate: ReportComplete, modified: ITimeSheetEntity[], deleted: ITimeSheetEntity[]) {
		if (modified && modified.length > 0) {
			parentUpdate.TimeSheetToSave = modified;
		}

		if (deleted && deleted.length > 0) {
			parentUpdate.TimeSheetToDelete = deleted;
		}
	}

	/**
	 * Determines if the given entity belongs to the specified parent.
	 * @param parentKey The parent ReportComplete object.
	 * @param entity The ITimeSheetEntity to check.
	 * @returns {boolean} Always returns true.
	 */
	public override isParentFn(parentKey: IReportEntity, entity: ITimeSheetEntity): boolean {
		return entity.ReportFk === parentKey.Id;
	}

	/**
	 * Retrieves the saved entities from the update object.
	 * @param complete The ReportComplete object containing saved entities.
	 * @returns {ITimeSheetEntity[]} Array of saved ITimeSheetEntity objects.
	 */
	public override getSavedEntitiesFromUpdate(complete: ReportComplete): ITimeSheetEntity[] {
		if (complete && complete.TimeSheetToSave) {
			return complete.TimeSheetToSave;
		}
		return [];
	}

	/**
	 * Provides the payload for loading data, based on the selected parent.
	 * @returns {object} The payload object with ReportFk or a default value.
	 */
	protected override provideLoadPayload(): object {
		const parentSelection = this.getSelectedParent();
		if (parentSelection) {
			return {
				ReportFk: parentSelection.Id,
			};
		}
		return {
			mainItemId: -1,
		};
	}

	/**
	 * Processes the loaded data and returns an array of ITimeSheetEntity.
	 * @param loaded The loaded data object from the backend.
	 * @returns {ITimeSheetEntity[]} Array of loaded ITimeSheetEntity objects.
	 */
	protected override onLoadSucceeded(loaded: object): ITimeSheetEntity[] {
		if (loaded) {
			return get(loaded, 'Main', []);
		}
		return [];
	}
}
