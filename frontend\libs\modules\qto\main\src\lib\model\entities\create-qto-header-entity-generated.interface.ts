/*
 * $Id$
 * Copyright(c) RIB Software GmbH
 */

export interface ICreateQtoHeaderEntityGenerated {
	/*
	 * BasGoniometerTypeFk
	 */
	BasGoniometerTypeFk?: number | null;

	/*
	 * BasRubricCategoryFk
	 */
	BasRubricCategoryFk?: number | null;

	/*
	 * BoqHeaderFk
	 */
	BoqHeaderFk?: number | null;

	/*
	 * BoqSource
	 */
	BoqSource?: number | null;

	/*
	 * BusinessPartnerFk
	 */
	BusinessPartnerFk?: number | null;

	/*
	 * ClerkFk
	 */
	ClerkFk?: number | null;

	/*
	 * Code
	 */
	Code?: string | null;

	/*
	 * ConHeaderFk
	 */
	ConHeaderFk?: number | null;

	/*
	 * ContractCode
	 */
	ContractCode?: string | null;

	/*
	 * Description
	 */
	Description?: string | null;

	/*
	 * NewBoqs
	 */
	// NewBoqs?: IBoqItemEntity[] | null;

	/*
	 * OrdHeaderFk
	 */
	OrdHeaderFk?: number | null;

	/*
	 * Package2HeaderFK
	 */
	Package2HeaderFK?: number | null;

	/*
	 * PackageFk
	 */
	PackageFk?: number | null;

	/*
	 * PrcBoqFk
	 */
	PrcBoqFk?: number | null;

	/*
	 * PrcHeaderFk
	 */
	PrcHeaderFk?: number | null;

	/*
	 * PrcHeaderFkOriginal
	 */
	PrcHeaderFkOriginal?: number | null;

	/*
	 * PrcStructureFk
	 */
	PrcStructureFk?: number | null;

	/*
	 * PrjBoqFk
	 */
	PrjBoqFk?: number | null;

	/*
	 * ProjectFk
	 */
	ProjectFk?: number | null;

	/*
	 * QtoTargetType
	 */
	QtoTargetType?: number | null;

	/*
	 * QtoType
	 */
	QtoType?: number | null;
}
