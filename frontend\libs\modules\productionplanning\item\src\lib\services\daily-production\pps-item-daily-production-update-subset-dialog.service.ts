import {
	ColumnDef,
	createLookup,
	FieldType, IEditorDialogResult,
	IGridDialogOptions, IGridDialogState,
	StandardDialogButtonId,
	UiCommonGridDialogService
} from '@libs/ui/common';
import { inject, Injectable } from '@angular/core';
import { PlatformHttpService, PlatformTranslateService } from '@libs/platform/common';
import { BasicsSharedUomLookupService} from '@libs/basics/shared';
import { isFunction } from 'lodash';

import { IPpsDailyProductionEntity } from '../../model/models';

@Injectable({
	providedIn: 'root'
})
export class PpsItemDailyProductionUpdateSubsetDialogService {

	private readonly https = inject(PlatformHttpService);
	private readonly translateService = inject(PlatformTranslateService);
	private readonly gridDialogService = inject(UiCommonGridDialogService);

	private generateGridColumns(): ColumnDef<IPpsDailyProductionEntity>[] {
		return [
			{
				id: 'PlannedStart',
				model: 'PlannedStart',
				label: {
					key: 'productionplanning.common.event.plannedStart',
					text: 'PlannedStart',
				},
				type: FieldType.DateUtc,
				sortable: true,
				visible: true,
			},
			{
				id: 'Description',
				model: 'Description',
				label: {
					key: 'cloud.common.descriptionInfo',
					text: 'Description',
				},
				type: FieldType.Description,
				sortable: true,
				visible: true,
			},
			{
				id: 'IsAssigned',
				model: 'IsAssigned',
				label: {
					key: 'productionplanning.item.dailyProduction.isAssigned',
					text: 'IsAssigned',
				},
				type: FieldType.Boolean,
				sortable: true,
				visible: true,
			},
			{
				id: 'FullyCovered',
				model: 'FullyCovered',
				label: {
					key: 'productionplanning.item.dailyProduction.fullyCovered',
					text: 'Fully Covered',
				},
				type: FieldType.Boolean,
				sortable: true,
				visible: true,
			},
			{
				id: 'PlanQty',
				model: 'PlanQty',
				label: {
					key: 'productionplanning.item.dailyProduction.planQty',
					text: 'Plan Quantity',
				},
				type: FieldType.Quantity,
				sortable: true,
				visible: true,
			},
			{
				id: 'RealQty',
				model: 'RealQty',
				label: {
					key: 'productionplanning.item.dailyProduction.realQty',
					text: 'Real Quantity',
				},
				type: FieldType.Quantity,
				sortable: true,
				visible: true,
			},
			{
				id: 'Difference',
				model: 'Difference',
				label: {
					key: 'productionplanning.item.dailyProduction.difference',
					text: 'Difference',
				},
				type: FieldType.Quantity,
				sortable: true,
				visible: true,
			},
			{
				id: 'UomFk',
				model: 'UomFk',
				label: {
					key: 'cloud.common.entityUoM',
					text: 'Uom',
				},
				type: FieldType.Lookup,
				lookupOptions: createLookup({
					dataServiceToken: BasicsSharedUomLookupService,
				}),
				sortable: true,
				visible: true,
			},
		];
	}

	public async openDialog(items: IPpsDailyProductionEntity[], loadFunc: unknown) {
		const infoGridDialogData: IGridDialogOptions<IPpsDailyProductionEntity> = {
			width: 'max',
			windowClass: 'grid-dialog',
			headerText: this.translateService.instant('productionplanning.item.dailyProduction.createSubSet').text,
			gridConfig: {
				uuid: '8a5bc558cac9437cbfd876b064128aad',
				columns: this.generateGridColumns(),
			},
			items: items,
			selectedItems: [],
			isReadOnly: false,
			resizeable: true,
		};

		await this.gridDialogService.show(infoGridDialogData)?.then(result => {
			if (result?.closingButtonId === StandardDialogButtonId.Ok) {
				this.handleOk(result, loadFunc);
			} else {
				this.handleCancel(result);
			}
		});
	}

	public async show(items: Promise<IPpsDailyProductionEntity[]>, loadFunc: unknown) {
		await this.openDialog(await items, loadFunc);
	}

	private handleOk(result:  IEditorDialogResult<IGridDialogState<IPpsDailyProductionEntity>>, loadFunc: unknown) {
		const subsets = result.value?.items;
		this.https.post<boolean>('productionplanning/productionset/dailyproduction/updatesubsets', subsets).then((res) =>{
			if (res) {
				if (isFunction(loadFunc)) {
					loadFunc();
					// if (platformPlanningBoardDataService.getPlanningBoardDataServiceByAssignmentServiceName('ppsItemDailyPlanningBoardAssignmentService') !== undefined) {
					// 	platformPlanningBoardDataService.getPlanningBoardDataServiceByAssignmentServiceName('ppsItemDailyPlanningBoardAssignmentService').load();
					// }
				}
			}
		});
	}

	private handleCancel(result: IEditorDialogResult<IGridDialogState<IPpsDailyProductionEntity>>) {

	}

}