{"extends": ["../../../../.eslintrc.json"], "ignorePatterns": ["!**/*"], "overrides": [{"files": ["*.ts"], "extends": ["plugin:@nrwl/nx/angular", "plugin:@angular-eslint/template/process-inline-templates", "../../../../.eslintrc.rules.json"], "rules": {"@angular-eslint/directive-selector": ["error", {"type": "attribute", "prefix": "qtoFormula", "style": "camelCase"}], "@angular-eslint/component-selector": ["error", {"type": "element", "prefix": "qto-formula", "style": "kebab-case"}]}}, {"files": ["*.html"], "extends": ["../../../../.eslintrc.rules.json"]}]}