import { Injectable } from '@angular/core';
import {
	DataServiceHierarchicalLeaf,
	IDataServiceEndPointOptions,
	IDataServiceOptions, IDataServiceRoleOptions, IEntityList, ServiceRole
} from '@libs/platform/data-access';
import { IPPSItemEntity } from '@libs/productionplanning/common';
import { IPpsDailyProductionEntity, PPSItemComplete } from '../../model/models';
import { PpsItemDataService } from '../pps-item-data.service';


@Injectable({
	providedIn: 'root',
})
export class PpsItemDailyProductionDataService extends DataServiceHierarchicalLeaf<IPpsDailyProductionEntity, IPPSItemEntity, PPSItemComplete> {
	public constructor(private ppsItemDataService: PpsItemDataService) {
		const options: IDataServiceOptions<IPpsDailyProductionEntity> = {
			apiUrl: 'productionplanning/productionset/dailyproduction',
			readInfo: <IDataServiceEndPointOptions>{
				endPoint: 'tree',
				usePost: false,
			},
			roleInfo: <IDataServiceRoleOptions<IPpsDailyProductionEntity>>{
				role: ServiceRole.Leaf,
				itemName: 'DailyProduction',
				parent: ppsItemDataService
			},
			entityActions: {
				createSupported: false,
				deleteSupported: false,
			},
		};

		super(options);
	}

	protected override provideLoadPayload(): object {
		return {
			parentId: this.getSelectedParent()?.Id
		};
	}

	private transferModification2Complete(complete: PPSItemComplete, modified: IPpsDailyProductionEntity[], deleted: IPpsDailyProductionEntity[]) {
		if (modified && modified.length > 0) {
			complete.DailyProductionToSave = modified;
		}
	}

	private takeOverUpdatedFromComplete(complete: PPSItemComplete, entityList: IEntityList<IPpsDailyProductionEntity>) {
		if (complete && complete.DailyProductionToSave && complete.DailyProductionToSave.length > 0) {
			entityList.updateEntities(complete.DailyProductionToSave);
		}
	}

	public getParentItem(){
		return this.getSelectedParent();
	}

}