/*
 * $Id$
 * Copyright(c) RIB Software GmbH
 */

import { IDescriptionInfo, IEntityBase } from '@libs/platform/common';

export interface IQtoTypeEntityGenerated extends IEntityBase {
	/*
	 * BasGoniometerTypeFk
	 */
	BasGoniometerTypeFk?: number | null;

	/*
	 * BasRubricCategoryFk
	 */
	BasRubricCategoryFk?: number | null;

	/*
	 * DescriptionInfo
	 */
	DescriptionInfo?: IDescriptionInfo | null;

	/*
	 * Id
	 */
	Id: number;

	/*
	 * IsDefault
	 */
	IsDefault: boolean;

	/*
	 * IsLive
	 */
	IsLive: boolean;

	/*
	 * Sorting
	 */
	Sorting: number;
}
