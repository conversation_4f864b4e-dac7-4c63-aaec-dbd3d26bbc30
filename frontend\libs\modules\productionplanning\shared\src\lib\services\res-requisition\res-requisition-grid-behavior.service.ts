/*
 * Copyright(c) RIB Software GmbH
 */

import { EntityContainerCommand, IEntityContainerBehavior, IEntityContainerLink, IGridContainerLink } from '@libs/ui/business-base';

import { IRequisitionEntity } from '@libs/resource/interfaces';
import { ICustomDialogOptions, InsertPosition, ItemType, StandardDialogButtonId, UiCommonDialogService } from '@libs/ui/common';
import { IEntityList, IEntityModification, IEntitySelection } from '@libs/platform/data-access';
import { ResRequisitionDialogComponent } from '../../components/res-requisition-dialog/res-requisition-dialog.component';
import { IEntityIdentification } from '@libs/platform/common';

import { get } from 'lodash';

export class PpsSharedResRequisitionBehavior implements IEntityContainerBehavior<IGridContainerLink<IRequisitionEntity>, IRequisitionEntity> {
	public constructor(
		private readonly modalDialogService: UiCommonDialogService,
		private readonly parentService: IEntitySelection<IEntityIdentification>,
		private readonly dataService: IEntityModification<IRequisitionEntity> & IEntitySelection<IRequisitionEntity> & IEntityList<IRequisitionEntity>,
	) { }

	public onCreate(containerLink: IEntityContainerLink<IRequisitionEntity>) {
		containerLink.uiAddOns.toolbar.addItemsAtId(
			[
				{
					type: ItemType.Item,
					id: 'createReference',
					caption: { key: 'cloud.common.createReference' },
					iconClass: 'tlb-icons ico-reference-add',
					permission: '#c',
					disabled: () => !containerLink.entityCreate?.canCreate(),
					fn: async () => {
						const dialogOptions: ICustomDialogOptions<void, ResRequisitionDialogComponent> = {
							id: '906a52ed41f14847942ac672ac0631a4', // '2e3d02ff2b8448268258f966f85d7ec5'
							headerText: 'productionplanning.common.resRequisitionDialogTitle',
							resizeable: true,
							width: '50%',
							windowClass: 'grid-dialog',
							backdrop: false,
							showCloseButton: true,
							bodyComponent: ResRequisitionDialogComponent,
							bodyProviders: [
								{
									provide: 'parentSelected',
									useValue: this.parentService.getSelectedEntity(),
								}
							],
							buttons: [
								{
									id: 'refresh',
									caption: { key: 'basics.common.button.refresh' },
									isDisabled: (info) => {
										return info.dialog.body.loading;
									},
									fn: async (event, info) => {
										await info.dialog.body.refresh();
									},
								},
								{
									id: StandardDialogButtonId.Ok,
									caption: { key: 'cloud.common.ok' },
									isDisabled: (info) => {
										return info.dialog.body.loading || !info.dialog.body.selectedEntities.length;
									},
									fn: async (event, info) => {
										const result = await info.dialog.body.ok();
										info.dialog.close(StandardDialogButtonId.Ok);

										if (result?.selectedEntities?.length > 0) {
											const originalList = this.dataService.getList();
											const toAppendList = result?.selectedEntities.filter((item) => {
												return !originalList.some((x) => x.Id === item.Id);
											});
											if (toAppendList.length > 0) {
												const ppsEventId = get(this.parentService.getSelectedEntity(), 'PpsEventFk');
												toAppendList.forEach((e) => {
													e.PpsEventFk = ppsEventId;
												});
												this.dataService.setModified(toAppendList);
												this.dataService.append(toAppendList);
											}
										}

									},
								},
								{
									id: StandardDialogButtonId.Cancel,
									caption: { key: 'ui.common.dialog.cancelBtn' },
								},
							],
						};
						this.modalDialogService.show(dialogOptions);
						// this.modalDialogService.show(dialogOptions)?.finally(async () => {
						// 	//option1
						// 	await this.dataService.reload();
						// 	//TODO: relaod or adding of the new entity here
						// });
					},
				},
				{
					type: ItemType.Item,
					id: 'deleteReference',
					caption: { key: 'cloud.common.deleteReference' },
					iconClass: 'tlb-icons ico-reference-delete',
					permission: '#c',
					disabled: () => !containerLink.entityDelete?.canDelete(),
					fn: async () => {
						const toDeletes = this.dataService.getSelection();
						toDeletes.forEach((item) => {
							item.PpsEventFk = null;
						});
						this.dataService.setModified(toDeletes);
						const originalList = this.dataService.getList();
						const newList = originalList.filter((item) => {
							return !toDeletes.some((toDelete) => toDelete.Id === item.Id);
						}
						);
						this.dataService.setList(newList);
					},
				},
			],
			EntityContainerCommand.CreateRecord,
			InsertPosition.Before,
		);
	}
}