{"extends": ["../../../../.eslintrc.json"], "ignorePatterns": ["!**/*"], "overrides": [{"files": ["*.ts"], "extends": ["plugin:@nx/angular", "plugin:@angular-eslint/template/process-inline-templates", "../../../../.eslintrc.rules.json"], "rules": {"@angular-eslint/directive-selector": ["error", {"type": "attribute", "prefix": "qtoMain", "style": "camelCase"}], "@angular-eslint/component-selector": ["error", {"type": "element", "prefix": "qto-main", "style": "kebab-case"}]}}, {"files": ["*.html"], "extends": ["plugin:@nx/angular-template", "../../../../.eslintrc.rules.json"], "rules": {}}]}