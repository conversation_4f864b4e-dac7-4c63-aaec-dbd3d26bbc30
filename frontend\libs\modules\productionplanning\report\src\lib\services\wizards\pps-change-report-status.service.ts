/*
 * Copyright(c) RIB Software GmbH
 */
import { BasicsSharedChangeStatusService, IStatusChangeOptions } from '@libs/basics/shared';
import { IReportEntity, ReportComplete } from '../../model/models';

import { ProductionplanningReportReportDataService } from '../productionplanning-report-report-data.service';
import { inject, Injectable } from '@angular/core';

@Injectable({
	providedIn: 'root',
})
export class PpsChangeReportStatusService extends BasicsSharedChangeStatusService<IReportEntity, IReportEntity, ReportComplete> {
	protected readonly dataService = inject(ProductionplanningReportReportDataService);
	protected statusConfiguration: IStatusChangeOptions<IReportEntity, ReportComplete> = {
		title: 'productionplanning.report.wizard.changeReportStatus',
		guid: 'bc1fdf73fcb2460ebd38a06afe1dc7b1',
		isSimpleStatus: true,
		statusName: 'mntreport',
		checkAccessRight: true,
		statusField: 'RepStatusFk',
		rootDataService: this.dataService,
	};
	public onStartChangeStatusWizard() {
		this.startChangeStatusWizard();
	}

	public override afterStatusChanged() {
		this.dataService.refreshSelected ? this.dataService.refreshSelected() : this.dataService.refreshAll();
	}
}
