/*
 * Copyright(c) RIB Software GmbH
 */

import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BasicsSharedModule } from '@libs/basics/shared';
import { PpsProductTemplateDialogLookupComponent } from './components/product-template/pps-product-template-dialog-lookup.component';

import { PpsFormworkDialogLookupComponent } from './components/process-configuration/pps-formwork-dialog-lookup/pps-formwork-dialog-lookup.component';
import { ResRequisitionDialogComponent } from './components/res-requisition-dialog/res-requisition-dialog.component';
import { GridComponent, UiCommonModule } from '@libs/ui/common';
// import { PlatformCommonModule } from '@libs/platform/common';



@NgModule({
	imports: [CommonModule, BasicsSharedModule,   
		UiCommonModule
		// , PlatformCommonModule
		, GridComponent
	],
	declarations: [PpsProductTemplateDialogLookupComponent, PpsFormworkDialogLookupComponent, ResRequisitionDialogComponent],
	exports: [PpsProductTemplateDialogLookupComponent, PpsFormworkDialogLookupComponent],
})
export class ProductionplanningSharedModule {}
