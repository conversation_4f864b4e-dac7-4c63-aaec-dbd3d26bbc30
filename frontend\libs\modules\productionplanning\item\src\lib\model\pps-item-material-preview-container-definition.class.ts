import { runInInjectionContext } from '@angular/core';
import {
	BasicsSharedPhotoEntityViewerComponent,
	PHOTO_ENTITY_VIEWER_OPTION_TOKEN
} from '@libs/basics/shared';
import { PlatformConfigurationService, ServiceLocator } from '@libs/platform/common';
import { IPPSItemEntity } from '@libs/productionplanning/common';
import { EntityContainerInjectionTokens } from '@libs/ui/business-base';
import { PpsItemDataService } from '../services/pps-item-data.service';

export class PpsItemMaterialPreviewContainerDefinition {
	private readonly configService = ServiceLocator.injector.get(PlatformConfigurationService);

	private readonly definition = {
		uuid: '07755a4c39524a00ac833c1908d44ff1',
		id: 'productionplanning.item.material.preview',
		title: {
			text: '*Material Preview',
			key: 'productionplanning.item.materialPreviewTitle'
		},
		containerType: BasicsSharedPhotoEntityViewerComponent,
		permission: '5907fffe0f9b44588254c79a70ba3af1',
		providers: [
			{
				provide: new EntityContainerInjectionTokens<IPPSItemEntity>().dataServiceToken,
				useExisting: PpsItemDataService
			},
			{
				provide: PHOTO_ENTITY_VIEWER_OPTION_TOKEN,
				useValue: {
					isSyncMode: false,
					isSingle: true,
					hideCreateEntity: true,
					hideDeleteEntity: true,
					hideChangeItem: true,
					blobFieldName: 'MaterialBlobsFk',
					dtoName: 'PPSItemDto',
					getUrl: this.configService.webApiBaseUrl + 'basics/material/preview/getblob',
				}
			}
		]
	};

	public getDefinition() {
		return this.definition;
	}
}

export const PPS_ITEM_MATERIAL_PREVIEW_CONTAINER_DEFINITION = runInInjectionContext(ServiceLocator.injector, () => new PpsItemMaterialPreviewContainerDefinition().getDefinition());

// todo: refresh the container when MdcMaterialFk of selected PU is changed, as related implementation of entity modified/changed in data service is missing.
// related old angularjs codes: itemDataService.registerMaterialFkChanged($scope.getFile);