/*
 * $Id$
 * Copyright(c) RIB Software GmbH
 */

export interface ICheckQtoDetailReferenceUniqueParamGenerated {
	/*
	 * Id
	 */
	Id?: number[] | null;

	/*
	 * IsCheckedSheet
	 */
	IsCheckedSheet?: boolean | null;

	/*
	 * LineIndex
	 */
	LineIndex?: number[] | null;

	/*
	 * LineReference
	 */
	LineReference?: string[] | null;

	/*
	 * PageNumber
	 */
	PageNumber?: number[] | null;

	/*
	 * QtoHeaderId
	 */
	QtoHeaderId?: number | null;
}
