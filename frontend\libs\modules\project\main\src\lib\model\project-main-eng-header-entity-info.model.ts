/*
 * Copyright(c) RIB Software GmbH
 */

import { EngHeaderEntityInfoFactoryService } from '@libs/productionplanning/shared';
import { IProjectEntity } from '@libs/project/interfaces';
import { ProjectMainDataService } from '@libs/project/shared';
import { EntityInfo } from '@libs/ui/business-base';

export const PROJECT_MAIN_ENG_HEADER_ENTITY_INFO: EntityInfo = EngHeaderEntityInfoFactoryService.create<IProjectEntity>({
    containerUuid: '17947c4e6d894d7792e79f18848fc3f8',
    formContainerUuid:'abfa68a6d1a34097ab6f87a57b0e97cf',
    permissionUuid: 'e48b688d24114e2da6ff2358f80cbcc9',
    parentServiceFn: (ctx) => ctx.injector.get(ProjectMainDataService),
});

