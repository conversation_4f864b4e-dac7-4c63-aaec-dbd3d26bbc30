/*
 * Copyright(c) RIB Software GmbH
 */

import { EntityInfo } from '@libs/ui/business-base';
import { ProductionplanningReportReportDataService } from '../services/productionplanning-report-report-data.service';
import { ProductionplanningReportReportBehavior } from '../behaviors/productionplanning-report-report-behavior.service';
import { IReportEntity } from './models';
import { prefixAllTranslationKeys } from '@libs/platform/common';
import { PpsReportValidationService } from '../services/validations/pps-report-validation.service';
import { createLookup, FieldType } from '@libs/ui/common';
import { BasicsSharedLookupOverloadProvider, BasicsSharedMountingReportStatusLookupService } from '@libs/basics/shared';
import { IBasicsCustomizeMountingReportStatusEntity } from '@libs/basics/interfaces';
import { ProjectSharedProjectLookupProviderService } from '@libs/project/shared';
import { inject, runInInjectionContext } from '@angular/core';

export const PRODUCTIONPLANNING_REPORT_REPORT_ENTITY_INFO: EntityInfo = EntityInfo.create<IReportEntity>({
	grid: {
		title: { key: 'productionplanning.report' + '.report.listTitle' },
		behavior: (ctx) => ctx.injector.get(ProductionplanningReportReportBehavior),
	},
	form: {
		title: { key: 'productionplanning.report' + '.report.detailTitle' },
		containerUuid: 'f32ffb6f21d34c7ab7aca13882ec61fe',
	},

	dataService: (ctx) => ctx.injector.get(ProductionplanningReportReportDataService),
	validationService: (ctx) => ctx.injector.get(PpsReportValidationService),
	dtoSchemeId: { moduleSubModule: 'ProductionPlanning.Report', typeName: 'ReportDto' },
	permissionUuid: 'a17a58e59a944f95ae9e0c7f627c9e1a',
	layoutConfiguration: async (context) => runInInjectionContext(context.injector, () => {
		const projectLookupProvider = inject(ProjectSharedProjectLookupProviderService);
		return {
			groups: [
				{
					gid: 'baseGroup',
					attributes: ['RepStatusFk', 'Code', 'DescriptionInfo', 'ActivityFk', 'ClerkFk', 'StartTime', 'EndTime', 'Remarks', 'MntRequisitionId', 'ProjectId'],
				},
				{
					gid: 'userDefTextGroup',
					attributes: ['Userdefined1', 'Userdefined2', 'Userdefined3', 'Userdefined4', 'Userdefined5'],
				},
			],
			overloads: {
				RepStatusFk: {
					type: FieldType.Lookup,
					lookupOptions: createLookup({
						dataServiceToken: BasicsSharedMountingReportStatusLookupService,
						displayMember: 'DescriptionInfo.Translated',
						imageSelector: {
							select(item: IBasicsCustomizeMountingReportStatusEntity): string {
								return item.Icon ? `status-icons ico-status${item.Icon.toString().padStart(2, '0')}` : '';
							},
							getIconType() {
								return 'css';
							},
						},
					}),
					readonly: true,
				},
				ClerkFk: BasicsSharedLookupOverloadProvider.providerBasicsClerkLookupOverload(true),
				ProjectId: {
					...projectLookupProvider.generateProjectLookup({
							lookupOptions: {
								displayMember: 'ProjectName',
								showClearButton: true,
								readonly:false,
							},
						}),
						additionalFields: [
							{
								displayMember: 'ProjectName',
								label: {
									key: 'cloud.common.entityProjectName',
								},
								column: true,
								row: false,
								singleRow: true,
							},
						],
				}
			},
			labels: {
				...prefixAllTranslationKeys('cloud.common.', {
					Code: { key: 'entityCode' },
					DescriptionInfo: { key: 'entityDescription' },
					ClerkFk: { key: 'entityClerk' },
					RepStatusFk: { key: 'entityStatus' },
					ProjectId: { key: 'entityProject' },
					Remarks: { key: 'entityRemark' },
					userDefTextGroup: { key: 'UserdefTexts' },
					Userdefined1: { key: 'entityUserDefined', params: { p_0: '1' } },
					Userdefined2: { key: 'entityUserDefined', params: { p_0: '2' } },
					Userdefined3: { key: 'entityUserDefined', params: { p_0: '3' } },
					Userdefined4: { key: 'entityUserDefined', params: { p_0: '4' } },
					Userdefined5: { key: 'entityUserDefined', params: { p_0: '5' } },
				}),
				...prefixAllTranslationKeys('productionplanning.report.', {
					StartTime: { key: 'report.StartTime' },
					EndTime: { key: 'report.EndTime' },
				}),
				...prefixAllTranslationKeys('productionplanning.mounting.', {
					ActivityFk: { key: 'entityActivity' },
					MntRequisitionId: { key: 'entityRequisition' },
				}),
			},
		};
	}),
});
