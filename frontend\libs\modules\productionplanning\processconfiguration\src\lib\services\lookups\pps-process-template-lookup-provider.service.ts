/*
 * $Id$
 * Copyright(c) RIB Software GmbH
 */

import { Injectable } from '@angular/core';
import { LazyInjectable } from '@libs/platform/common';
import { createLookup, FieldType, ILookupContext, ILookupOptions, TypedConcreteFieldOverload, UiCommonLookupDataFactoryService } from '@libs/ui/common';
import { IPpsProcessTemplateLookupProvider, PPS_PROCESS_TEMPLATE_LOOKUP_PROVIDER_TOKEN, ProcessTemplateEntity } from '@libs/productionplanning/interfaces';

@LazyInjectable({
	token: PPS_PROCESS_TEMPLATE_LOOKUP_PROVIDER_TOKEN,
	useAngularInjection: true,
})
@Injectable({
	providedIn: 'root',
})
export class PpsProcessTemplateLookupProviderService implements IPpsProcessTemplateLookupProvider {
	public constructor(private readonly _lookupFactory: UiCommonLookupDataFactoryService) {}

	public provideDialogLookupOverload<T extends object>(options?: ILookupOptions<ProcessTemplateEntity, T>): TypedConcreteFieldOverload<T> {
		return {
			type: FieldType.Lookup,
			lookupOptions: createLookup<T, ProcessTemplateEntity>({
				dataService: this._lookupFactory.fromLookupType('ProcessTemplate', {
					uuid: 'a48fe5d87b514a5a945ba8173fd8a5ff',
					valueMember: 'Id',
					displayMember: 'DescriptionInfo.Translated',
					showClearButton: true,
					showDialog: true,
					dialogOptions: {
						headerText: {
							text: 'Process Template Lookup',
							key: 'productionplanning.processconfiguration.processTemplateGridContainerTitle',
						},
					},
					gridConfig: {
						columns: [
							{
								id: 'description',
								model: 'DescriptionInfo',
								type: FieldType.Description,
								label: {
									text: 'Description',
									key: 'cloud.common.entityDescription',
								},
								readonly: true,
								visible: true,
								sortable: true,
								searchable: true,
								formatterOptions: {
									field: 'DescriptionInfo.Translated',
								},
							},
						],
					},
				}),
				//TODO: with getSearchList the clientSidefilter is unavailable, might later find a new way or wait to fix
				clientSideFilter: {
					execute(item: ProcessTemplateEntity, context: ILookupContext<ProcessTemplateEntity, T>): boolean {
						return item.IsLive;
					},
				},
				...options,
			}),
		};
	}
}
