
import {inject, Injectable} from '@angular/core';
import {BasicsSharedChangeStatusService, IStatusChangeOptions} from '@libs/basics/shared';

import { PpsItemDataService } from '../pps-item-data.service';
import { IPPSItemEntity } from '@libs/productionplanning/common';
import { PPSItemComplete } from '../../model/models';
import { PpsItemProductDataService } from '../product/pps-item-product-data.service';
import { IPpsProductEntityGenerated } from '@libs/productionplanning/shared';


@Injectable({
	providedIn: 'root'
})
export class PpsItemProductChangeStatusWizardService extends BasicsSharedChangeStatusService<IPpsProductEntityGenerated, IPPSItemEntity, PPSItemComplete> {
	protected readonly dataService = inject(PpsItemProductDataService);
	protected readonly rootDataService = inject(PpsItemDataService);

	protected statusConfiguration: IStatusChangeOptions<IPPSItemEntity, PPSItemComplete> = {
		title: 'productionplanning.item.wizard.changeItemStatus',
		guid: '0bd0c22574f841b4a907de00e5af3f46',
		isSimpleStatus: true,
		statusName: 'ppsproduct',
		checkAccessRight: true,
		statusField: 'ProductStatusFk',
		updateUrl: 'productionplanning/common/wizard/changeproductstatus',
		rootDataService: this.rootDataService
	};

	public onStartChangeStatusWizard() {
		this.startChangeStatusWizard();
	}

	public override afterStatusChanged() {
		//this.dataService.refreshSelected ? this.dataService.refreshSelected() : this.dataService.refreshAll();
	}
}