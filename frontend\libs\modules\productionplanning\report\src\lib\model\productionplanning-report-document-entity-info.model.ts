/*
 * Copyright(c) RIB Software GmbH
 */

import { EntityInfo } from '@libs/ui/business-base';
import { ProductionplanningShareDocumentEntityInfoFactory } from '@libs/productionplanning/shared';
import { ProductionplanningReportReportDataService } from '../services/productionplanning-report-report-data.service';
import { IReportEntity } from './models';


export const PRODUCTIONPLANNING_REPORT_DOCUMENT_ENTITY_INFO: EntityInfo = ProductionplanningShareDocumentEntityInfoFactory.create<IReportEntity>({
	containerUuid: '72366c321e554a7d86da04e8fc996047',
	permissionUuid: '5640a72648e24f21bf3985624c4d0fdf',
	gridTitle: { key: 'productionplanning.report.document.reportDocumentListTitle', text: 'Report:Document' },
	foreignKey: 'MntReportFk',
	parentServiceFn: (ctx) => {
		return ctx.injector.get(ProductionplanningReportReportDataService);
	},
});
