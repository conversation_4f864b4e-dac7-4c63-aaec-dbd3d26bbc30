import { Injectable } from '@angular/core';
import { QtoMainHeaderCreationConfigEntity } from '../model/qto-main-header-creation-config-entity.class';

@Injectable({
	providedIn: 'root',
})
export class QtoMainCreateDialogDataService {
	public setConfig(config: QtoMainHeaderCreationConfigEntity) {}

	public setIsGeneratedState(): void {}

	public setDefaultQtoPurposeTypeId(): void {}

	public setQtoTypeInfo(): void {}

	public showDialog(): void {}
}
