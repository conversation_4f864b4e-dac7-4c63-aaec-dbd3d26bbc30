// remark: current file is copied from basics-material-material-group-data.service in basics.material,
// should be replaced by other way(like <PERSON>zy<PERSON>n<PERSON>Token from basics.material module) in the future
/*
 * Copyright(c) RIB Software GmbH
 */

import { Injectable } from '@angular/core';
import { DataServiceFlatLeaf, IDataServiceChildRoleOptions, IDataServiceOptions, ServiceRole } from '@libs/platform/data-access';
import { PpsMaterialRecordDataService } from '../material/material-record-data.service';
import { PpsMaterialComplete } from '../../model/entities/pps-material-complete.class';
import { IMaterialNewEntity } from '../../model/entities/material-new-entity.interface';
import { IPpsMaterialMappingEntity } from '../../model/entities/pps-material-mapping-entity.interface';

/**
 * Material group data service
 */
@Injectable({
	providedIn: 'root',
})
export class PpsMaterialMappingDataService extends DataServiceFlatLeaf<IPpsMaterialMappingEntity, IMaterialNewEntity, PpsMaterialComplete> {
	/**
	 * The constructor
	 */
	public constructor(private readonly _parentService: PpsMaterialRecordDataService) {
		const options: IDataServiceOptions<IPpsMaterialMappingEntity> = {
			apiUrl: 'productionplanning/ppsmaterial/ppsmaterialmapping',
			roleInfo: <IDataServiceChildRoleOptions<IPpsMaterialMappingEntity, IMaterialNewEntity, PpsMaterialComplete>>{
				role: ServiceRole.Leaf,
				itemName: 'PpsMaterialMapping',
				parent: _parentService,
			},
			createInfo: {
				endPoint: 'create',
				usePost: true,
				prepareParam: (identity) => {
					return {
						Id: _parentService.getSelectedEntity()?.PpsMaterial?.Id || -1,
					};
				},
			},
			readInfo: {
				endPoint: 'list',
				usePost: false,
				prepareParam: (ident) => {
					return { mainItemId: _parentService.getSelectedEntity()?.PpsMaterial?.Id || -1 };
				},
			},
		};
		super(options);
	}
}
