import { IInitializationContext } from '@libs/platform/common';
import { runInInjectionContext } from '@angular/core';
import { PpsItemDataService } from '../../services/pps-item-data.service';
import { PpsUpstreamItemDataServiceManager } from '../../services/upstream-item/pps-upstream-item-data-service-manager.service';
import { PpsUpstreamItemChangeStatusWizardService } from '../../services/upstream-item/pps-upstream-item-change-status-wizard.service';
import {
	PpsItemChangeProjectDocumentStatusWizardService
} from '../../services/wizards/pps-item-change-project-document-status-wizard.service';
import { PpsItemEnableWizardService } from './pps-item-enable-wizard.service';
import { PpsItemDisableWizardService } from './pps-item-disable-wizard.service';
import { PpsItemChangeStatusWizardService } from './pps-item-change-status-wizard.service';
import {
	PpsItemDrawingComponentDisableWizardService
} from '../../services/wizards/pps-item-drawing-component-disable-wizard.service';
import {
	PpsItemDrawingComponentEnableWizardService
} from '../../services/wizards/pps-item-drawing-component-enable-wizard.service';
import { PpsItemProductEnableWizardService } from '../../services/wizards/pps-item-product-enable-wizard.service';
import { PpsItemProductDisableWizardService } from '../../services/wizards/pps-item-product-disable-wizard.service';
import {
	PpsItemProductChangeStatusWizardService
} from '../../services/wizards/pps-item-product-change-status-wizard.service';

export class PpsItemWizard {

	public enablePpsItem(context: IInitializationContext) {
		const service = context.injector.get(PpsItemEnableWizardService);
		service.onStartEnableWizard();
	}

	public disablePpsItem(context: IInitializationContext) {
		const service = context.injector.get(PpsItemDisableWizardService);
		service.onStartDisableWizard();
	}

	public changePpsItemStatus(context: IInitializationContext) {
		const service = context.injector.get(PpsItemChangeStatusWizardService);
		service.onStartChangeStatusWizard();
	}

	public changeUpstreamStatus(context: IInitializationContext) {
		const upstreamItemDataServ = PpsUpstreamItemDataServiceManager.getDataService(
			{
				containerUuid: '23edab57edgb492d84r2gv47e734fh8u',
				parentServiceFn: (ctx) => {
					return ctx.injector.get(PpsItemDataService);
				},
			},
			context
		);
		const service = runInInjectionContext(context.injector, () => new PpsUpstreamItemChangeStatusWizardService(
			upstreamItemDataServ
		));
		service.onStartChangeStatusWizard();
	}

	public changeDocumentProjectStatus(context: IInitializationContext) {
		const dataService = context.injector.get(PpsItemChangeProjectDocumentStatusWizardService);
		dataService.onStartChangeStatusWizard();
	}

	public enableItemProduct(context: IInitializationContext) {
		const service = context.injector.get(PpsItemProductEnableWizardService);
		service.onStartEnableWizard();
	}

	public disableItemProduct(context: IInitializationContext) {
		const service = context.injector.get(PpsItemProductDisableWizardService);
		service.onStartDisableWizard();
	}

	public changeItemProductStatus(context: IInitializationContext) {
		const service = context.injector.get(PpsItemProductChangeStatusWizardService);
		service.onStartChangeStatusWizard();
	}

	public enableDrawingComponent(context: IInitializationContext) {
		const service = context.injector.get(PpsItemDrawingComponentEnableWizardService);
		service.onStartEnableWizard();
	}

	public disableDrawingComponent(context: IInitializationContext) {
		const service = context.injector.get(PpsItemDrawingComponentDisableWizardService);
		service.onStartDisableWizard();
	}

}