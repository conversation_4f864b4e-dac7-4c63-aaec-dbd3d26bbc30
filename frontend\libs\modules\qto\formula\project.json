{"name": "modules-qto-formula", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/modules/qto/formula/src", "prefix": "qto-formula", "tags": [], "projectType": "library", "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/modules/qto/formula/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}}}