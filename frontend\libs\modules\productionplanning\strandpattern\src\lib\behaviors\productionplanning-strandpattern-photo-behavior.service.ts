/*
 * Copyright(c) RIB Software GmbH
 */

import { Injectable, inject } from '@angular/core';
import { IEntityContainerBehavior, IGridContainerLink } from '@libs/ui/business-base';

import { ISearchPayload } from '@libs/platform/common';
import { ProductionplanningStrandpatternEntity } from '../model/productionplanning-strandpattern-entity.class';
import { ProductionplanningStrandpatternDataService } from '../services/productionplanning-strandpattern-data.service';

@Injectable({
	providedIn: 'root',
})
export class ProductionplanningStrandpatternPhotoBehavior implements IEntityContainerBehavior<IGridContainerLink<ProductionplanningStrandpatternEntity>, ProductionplanningStrandpatternEntity> {
	private dataService: ProductionplanningStrandpatternDataService;
	

	private searchPayload: ISearchPayload = {
		executionHints: false,
		filter: '',
		includeNonActiveItems: false,

		isReadingDueToRefresh: false,
		pageNumber: 0,
		pageSize: 100,
		pattern: '',
		pinningContext: [],
		projectContextId: null,
		useCurrentClient: true,
	};

	public constructor() {
		this.dataService = inject(ProductionplanningStrandpatternDataService);
	}

	public onCreate(containerLink: IGridContainerLink<ProductionplanningStrandpatternEntity>): void {
		
	}

}
