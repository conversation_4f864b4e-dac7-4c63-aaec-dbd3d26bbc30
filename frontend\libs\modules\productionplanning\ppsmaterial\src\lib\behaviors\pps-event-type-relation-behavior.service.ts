/*
 * Copyright(c) RIB Software GmbH
 */

import { Injectable, InjectionToken } from '@angular/core';
import { IEntityContainerBehavior, IGridContainerLink } from '@libs/ui/business-base';
import { IPpsEventTypeRelEntity } from '../model/models';

export const PPS_EVENT_TYPE_RELATION_BEHAVIOR_TOKEN = new InjectionToken<PpsEventTypeRelationBehavior>('ppsEventTypeRelationBehavior');

@Injectable({
	providedIn: 'root'
})
export class PpsEventTypeRelationBehavior implements IEntityContainerBehavior<IGridContainerLink<IPpsEventTypeRelEntity>, IPpsEventTypeRelEntity> {

}