/*
 * Copyright(c) RIB Software GmbH
 */

import { IEntityBase, IDescriptionInfo } from '@libs/platform/common';

export interface IReportEntityGenerated extends IEntityBase {
	/**
	 * ActivityFk
	 */
	ActivityFk: number;

	/**
	 * ClerkFk
	 */
	ClerkFk: number;

	/**
	 * Code
	 */
	Code: string;

	/**
	 * Deletable
	 */
	Deletable: boolean;

	/**
	 * DescriptionInfo
	 */
	DescriptionInfo?: IDescriptionInfo | null;

	/**
	 * EndTime
	 */
	EndTime: Date | string;

	/**
	 * Id
	 */
	Id: number;

	/**
	 * MntRequisitionId
	 */
	MntRequisitionId: number;

	/**
	 * PermissionObjectInfo
	 */
	PermissionObjectInfo?: string | null;

	/**
	 * ProjectId
	 */
	ProjectId: number;

	/**
	 * Remarks
	 */
	Remarks?: string | null;

	/**
	 * RepStatusFk
	 */
	RepStatusFk: number;

	/**
	 * StartTime
	 */
	StartTime: Date | string;

	/**
	 * Userdefined1
	 */
	Userdefined1?: string | null;

	/**
	 * Userdefined2
	 */
	Userdefined2?: string | null;

	/**
	 * Userdefined3
	 */
	Userdefined3?: string | null;

	/**
	 * Userdefined4
	 */
	Userdefined4?: string | null;

	/**
	 * Userdefined5
	 */
	Userdefined5?: string | null;
}
