/*
 * Copyright(c) RIB Software GmbH
 */
import {
	DataServiceFlatLeaf,
	IDataServiceChildRoleOptions,
	IDataServiceEndPointOptions,
	IDataServiceOptions,
	ServiceRole
} from '@libs/platform/data-access';

import { Injectable } from '@angular/core';
import { isNull } from 'lodash';
import { IMaterialNewEntity, IPpsMaterialCompEntity, PpsMaterialComplete } from '../../model/models';
import { PpsMaterialRecordDataService } from '../material/material-record-data.service';

@Injectable({
	providedIn: 'root'
})
export class PpsMaterialCompatibilityDataService extends DataServiceFlatLeaf<IPpsMaterialCompEntity, IMaterialNewEntity, PpsMaterialComplete> {

	private parentService: PpsMaterialRecordDataService;

	public constructor(parentService: PpsMaterialRecordDataService) {
		const options: IDataServiceOptions<IPpsMaterialCompEntity> = {
			apiUrl: 'productionplanning/ppsmaterial/ppsmaterialcomp',
			readInfo: <IDataServiceEndPointOptions>{
				endPoint: 'list',
				usePost: false,
			},
			roleInfo: <IDataServiceChildRoleOptions<IPpsMaterialCompEntity, IMaterialNewEntity, PpsMaterialComplete>>{
				role: ServiceRole.Leaf,
				itemName: 'PpsMaterialComp',
				parent: parentService
			}
		};

		super(options);
		this.parentService = parentService;
	}

	protected override provideCreatePayload(): object {
		return {
			Id: this.getSelectedParent()?.PpsMaterial?.Id
		};
	}

	protected override onCreateSucceeded(created: IPpsMaterialCompEntity): IPpsMaterialCompEntity {
		return created;
	}

	protected override provideLoadPayload(): object {
		return {
			mainItemId: this.getSelectedParent()?.PpsMaterial?.Id
		};
	}

	protected override onLoadSucceeded(loaded: object): IPpsMaterialCompEntity[] {
		return loaded as IPpsMaterialCompEntity[];
	}

	public override isParentFn(parentKey: IMaterialNewEntity, entity: IPpsMaterialCompEntity): boolean {
		return true;
		// return entity.PpsMaterialProductFk === parentKey?.PpsMaterial?.Id;
	}

	public override registerByMethod(): boolean {
		return true;
	}

	public override registerModificationsToParentUpdate(parentUpdate: PpsMaterialComplete, modified: IPpsMaterialCompEntity[], deleted: IPpsMaterialCompEntity[]): void {
		if (modified && modified.length > 0) {
			parentUpdate.PpsMaterialCompToSave = modified;
		}

		if (deleted && deleted.length > 0) {
			parentUpdate.PpsMaterialCompToDelete = deleted;
		}
	}

	public override getSavedEntitiesFromUpdate(parentUpdate: PpsMaterialComplete): IPpsMaterialCompEntity[] {
		if (parentUpdate && !isNull(parentUpdate.PpsMaterialCompToSave)) {
			return parentUpdate.PpsMaterialCompToSave ?? [];
		}
		return [];
	}

	public getSelectedMaterial(): IMaterialNewEntity | undefined {
		return this.getSelectedParent();
	}

	public getMaterialById(id: number): IMaterialNewEntity | undefined {
		return this.parentService.getList().find(e => e.Id === id);
	}
}