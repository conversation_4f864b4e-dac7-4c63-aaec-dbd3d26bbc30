/*
 * Copyright(c) RIB Software GmbH
 */

import { inject, Injectable } from '@angular/core';
import { EntityContainerCommand, IEntityContainerBehavior, IGridContainerLink } from '@libs/ui/business-base';
import { IReportEntity } from '../model/models';
import { ProductionplanningReportReportDataService } from '../services/productionplanning-report-report-data.service';
import { ItemType } from '@libs/ui/common';


@Injectable({
	providedIn: 'root',
})
export class ProductionplanningReportReportBehavior implements IEntityContainerBehavior<IGridContainerLink<IReportEntity>, IReportEntity> {
	private readonly dataService = inject(ProductionplanningReportReportDataService);

	public onCreate(containerLink: IGridContainerLink<IReportEntity>): void {
		containerLink.uiAddOns.toolbar.addItemsAtId(
			[
				{
					caption: { key: 'cloud.desktop.watchlist.addtowatchlisttp' },
					hideItem: false,
					iconClass: 'tlb-icons ico-watchlist-add',
					id: 't-addtowatchlist',
					fn: () => {
						//todo: watchlist add to watchlist
						throw new Error('This method is not implemented');
					},
					sort: 120,
					type: ItemType.Item,
				},
				{
					caption: { key: 'cloud.desktop.pinningDesktopDialogHeader' },
					hideItem: false,
					iconClass: 'tlb-icons ico-pin2desktop',
					id: 'pinningTemplate',
					fn: () => {
						//todo: Pin to desktop as a title
						throw new Error('This method is not implemented');
					},
					sort: 120,
					type: ItemType.Item,
				},
				{
					caption: { text: 'Pin Selected Item' },
					hideItem: false,
					iconClass: 'tlb-icons ico-set-prj-context',
					id: 'pinningSelected',
					fn: () => {
						//todo: Pin Selected Item
						throw new Error('This method is not implemented');
					},
					sort: 130,
					type: ItemType.Item,
				},
				{
					caption: { key: 'cloud.common.bulkEditor.title' },
					hideItem: false,
					iconClass: 'type-icons ico-construction51',
					id: 'bulkEditor',
					fn: () => {
						//todo: Bulk Editor
						throw new Error('This method is not implemented');
					},
					sort: 140,
					type: ItemType.Item,
				},
			],
			EntityContainerCommand.Settings,
		);
	}
}
