
import { inject, Injectable } from '@angular/core';
import { BasicsSharedSimpleActionWizardService, ISimpleActionOptions } from '@libs/basics/shared';

import { IPpsProductEntityGenerated } from '@libs/productionplanning/shared';
import { PpsItemProductDataService } from '../product/pps-item-product-data.service';


@Injectable({
	providedIn: 'root'
})
export class PpsItemProductDisableWizardService extends BasicsSharedSimpleActionWizardService<IPpsProductEntityGenerated> {

	private readonly productDataService = inject(PpsItemProductDataService);

	public onStartDisableWizard(): void {
		const option: ISimpleActionOptions<IPpsProductEntityGenerated> = {
			headerText: 'cloud.common.enableRecord',
			codeField: 'Code',
			doneMsg: 'productionplanning.common.product.wizard.disableProductDone',
			nothingToDoMsg: 'productionplanning.common.product.wizard.productAlreadyDisabled',
			questionMsg: 'cloud.common.questionDisableSelection'
		};
		this.startSimpleActionWizard(option);
	}

	public override filterToActionNeeded(selected: IPpsProductEntityGenerated[]): IPpsProductEntityGenerated[] {
		const filteredSelection: IPpsProductEntityGenerated[] = [];
		selected.forEach(item => {
			if (item.IsLive) {
				filteredSelection.push(item);
			}
		});
		return filteredSelection;
	}

	public override getSelection(): IPpsProductEntityGenerated[] {
		return this.productDataService.getSelection();
	}

	public override performAction(filtered: IPpsProductEntityGenerated[]): void {
		filtered.forEach(item => {
			item.IsLive = false;
			this.productDataService.setModified(item);
		});
	}

	public override postProcess(): void {
	}

}