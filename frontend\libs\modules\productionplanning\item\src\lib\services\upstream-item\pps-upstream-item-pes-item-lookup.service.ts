import { Injectable } from '@angular/core';
import { BasicsSharedUomLookupService } from '@libs/basics/shared';
import { ProjectSharedLookupService } from '@libs/project/shared';
import { createLookup, FieldType, UiCommonLookupTypeDataService } from '@libs/ui/common';
import { IPesItemPpsUpstrmIdEntity } from '../../model/entities/pes-item-pps-upstrmid.interface';
import { IIdentificationData } from '@libs/platform/common';
import { Observable } from 'rxjs';

@Injectable({
	providedIn: 'root'
})
export class PpsUpstreamItemPesItemLookupService<TEntity extends object> extends UiCommonLookupTypeDataService<IPesItemPpsUpstrmIdEntity, TEntity> {

	public constructor() {
		super('PesItemWithPpsUpStream', {
			uuid: '3819a8a5317c4579908f4d2e2f95d757',
			idProperty: 'Id',
			valueMember: 'Id',
			displayMember: 'Description1',
			gridConfig: {
				columns: [{
					id: 'ItemNo',
					model: 'ItemNo',
					type: FieldType.Description,
					label: { text: 'procurement.pes.entityItemNo' },
					sortable: true,
					visible: true,
					readonly: true
				}, {
					id: 'Description2',
					model: 'Description2',
					type: FieldType.Description,
					label: { text: 'cloud.common.entityDescription' },
					sortable: true,
					visible: true,
					readonly: true
				}, {
					id: 'Description2',
					model: 'Description2',
					type: FieldType.Description,
					label: { text: 'cloud.common.entityDescription2' },
					sortable: true,
					visible: true,
					readonly: true
				}, {
					id: 'projectNo',
					model: 'PrjProjectFk',
					type: FieldType.Lookup,
					lookupOptions: createLookup({
						dataServiceToken: ProjectSharedLookupService,
						displayMember: 'ProjectNo',
					}),
					label: { key: 'cloud.common.entityProjectNo' },
					sortable: true,
					readonly: true,
				},
				{
					id: 'projectName',
					model: 'PrjProjectFk',
					type: FieldType.Lookup,
					lookupOptions: createLookup({
						dataServiceToken: ProjectSharedLookupService,
						displayMember: 'ProjectName',
					}),
					label: { key: 'cloud.common.entityProjectName' },
					sortable: true,
					readonly: true,
				}, {
					id: 'Quantity',
					model: 'Quantity',
					type: FieldType.Quantity,
					label: { text: 'cloud.common.entityQuantity' },
					sortable: true,
					visible: true,
					readonly: true
				}, {
					id: 'BasUomFk',
					model: 'BasUomFk',
					type: FieldType.Lookup,
					lookupOptions: createLookup({
						dataServiceToken: BasicsSharedUomLookupService,
					}),
					label: { text: 'Uom', key: 'cloud.common.entityUoM' },
					sortable: true,
					visible: true,
					readonly: true,
				},]
			},
			dialogOptions: {
				headerText: {
					text: '*Pes Item',
					key: 'productionplanning.common.pesItemLookupDialogTitle',
				},
			},
			showDialog: true,
		});
	}

	// remark: the related  url(provided from server side) for 'getItemByKey' is like 'basics/lookupdata/master/getitembykey?lookup=pesitemwithppsupstream&id=1000001', so we have to override method getItemByKey
	public override getItemByKey(key: IIdentificationData): Observable<IPesItemPpsUpstrmIdEntity> {
		return new Observable(subscriber => {
			const cacheItem = this.cache.getItem(key);

			if (cacheItem) {
				this.completeWithValue(subscriber, cacheItem);
			} else {
				const url = this.generateOldUrl('getitembykey')+`&id=${key.id}`;

				this.get(url).subscribe(item => {
					if (item) {
						const entity = this.mapEntity(item);
						this.processItems([entity]);
						this.cache.setItem(entity);
						this.completeWithValue(subscriber, entity);
					} else {
						subscriber.error(new Error(`Item with key ${key.id} not found`));
					}
				});
			}
		});
	}

	protected oldBaseUrl = 'basics/lookupdata/master/';

	protected generateOldUrl(action: string) {
		return `${this.oldBaseUrl}${action}?lookup=${this.lookupType}`;
	}

}
