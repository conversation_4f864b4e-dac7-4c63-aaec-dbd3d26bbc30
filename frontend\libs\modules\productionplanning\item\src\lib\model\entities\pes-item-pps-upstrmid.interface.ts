/*
 * $Id$
 * Copyright(c) RIB Software GmbH
 */

import { IDescriptionInfo } from '@libs/platform/common';

export interface IPesItemPpsUpstrmIdEntity  {

	/*
	 * Id
	 */
	Id: number;

	/*
	 * ItemNo
	 */
	ItemNo: number;

	/*
	 * Description1
	 */
	Description1?: IDescriptionInfo | null;

	/*
	 * Description2
	 */
	Description2?: IDescriptionInfo | null;

	/*
	 * PesStatusFk
	 */
	PesStatusFk: number | null;

	/*
	 * PrjProjectFk
	 */
	PrjProjectFk?: number | null;

	/*
	 * Quantity
	 */
	Quantity: number;

	/*
	 * BasUomFk
	 */
	BasUomFk: number;

}
