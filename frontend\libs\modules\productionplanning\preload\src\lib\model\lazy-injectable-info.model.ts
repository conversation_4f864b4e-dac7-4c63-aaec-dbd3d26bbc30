/*
 * Copyright(c) RIB Software GmbH
 */

import { LazyInjectableInfo, ILazyInjectionContext, PlatformModuleManagerService } from '@libs/platform/common';
import { ENGINEERING_HEADER_LOOKUP_PROVIDER_TOKEN } from '@libs/productionplanning/shared';
import { PPS_PROCESS_TEMPLATE_LOOKUP_PROVIDER_TOKEN } from '@libs/productionplanning/interfaces';


export const LAZY_INJECTABLES: LazyInjectableInfo[] =[
LazyInjectableInfo.create('productionplanning.engineering.EngineeringHeaderLookupProviderService', ENGINEERING_HEADER_LOOKUP_PROVIDER_TOKEN, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/productionplanning/engineering');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.EngineeringHeaderLookupProviderService) : null;
		
	}),

	LazyInjectableInfo.create('productionplanning.processconfiguration.PpsProcessTemplateLookupProviderService', PPS_PROCESS_TEMPLATE_LOOKUP_PROVIDER_TOKEN, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/productionplanning/processconfiguration');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.PpsProcessTemplateLookupProviderService) : null;
		
	}),
];
 