/*
 * Copyright(c) RIB Software GmbH
 */

import { EntityInfo } from '@libs/ui/business-base';
import { IReportEntity, ReportComplete } from './models';
import { BasicsSharedUserFormDataEntityInfoFactory, Rubric } from '@libs/basics/shared';
import { ProductionplanningReportReportDataService } from '../services/productionplanning-report-report-data.service';

export const PPS_REPORT_FORM_DATA_ENTITY_INFO: EntityInfo = BasicsSharedUserFormDataEntityInfoFactory.create<IReportEntity, ReportComplete>({
	containerUuid: '07ce62e5a3e143279fe4001eb670b317',
	permissionUuid: 'a17a58e59a944f95ae9e0c7f627c9e1a',
	gridTitle: { key: 'productionplanning.report.formDataLisTitle', text: '*Report: Form Data' },
	rubric: Rubric.Installationreport,
	parentServiceFn: (ctx) => {
		return ctx.injector.get(ProductionplanningReportReportDataService);
	},
});
