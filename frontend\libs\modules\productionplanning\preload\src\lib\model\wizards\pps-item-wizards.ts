/*
 * Copyright(c) RIB Software GmbH
 */

import {IInitializationContext, IWizard} from '@libs/platform/common';

export const PPS_ITEM_WIZARDS: IWizard[] =
	[
		{
			uuid: '18fbf93276074426b1d613284329dedb',
			name: 'enablePpsItem',
			execute(context: IInitializationContext) {
				return import('@libs/productionplanning/item').then((module) => new module.PpsItemWizard().enablePpsItem(context));
			}
		},
		{
			uuid: '0247d02a88d244c186636726d7e6106b',
			name: 'disablePpsItem',
			execute(context: IInitializationContext) {
				return import('@libs/productionplanning/item').then((module) => new module.PpsItemWizard().disablePpsItem(context));
			}
		},
		{
			uuid: 'b441e44b1c7d4f8e902b14bbc376a186',
			name: 'changePpsItemStatus',
			execute(context: IInitializationContext) {
				return import('@libs/productionplanning/item').then((module) => new module.PpsItemWizard().changePpsItemStatus(context));
			}
		},
		{
			uuid: '9065e7dd71ab49eba2b6adc4f4001724',
			name: 'changeUpstreamStatus',
			execute(context): Promise<void> | undefined {
				return import('@libs/productionplanning/item').then((module) => new module.PpsItemWizard().changeUpstreamStatus(context));
			}
		},
		{
			uuid: '1a7923ae9b7b4ec3b6421e3255741e72',
			name: 'changeStatusForProjectDocument',
			execute(context: IInitializationContext) {
				return import('@libs/productionplanning/item').then((module) => new module.PpsItemWizard().changeDocumentProjectStatus(context));
			}
		},
		{
			uuid: '',
			name: 'changeItemProductStatus',
			execute(context: IInitializationContext) {
				return import('@libs/productionplanning/item').then((module) => new module.PpsItemWizard().changeItemProductStatus(context));
			}
		},
		{
			uuid: '',
			name: 'enableItemProduct',
			execute(context: IInitializationContext) {
				return import('@libs/productionplanning/item').then((module) => new module.PpsItemWizard().enableItemProduct(context));
			}
		},
		{
			uuid: '',
			name: 'disableItemProduct',
			execute(context: IInitializationContext) {
				return import('@libs/productionplanning/item').then((module) => new module.PpsItemWizard().disableItemProduct(context));
			}
		},
		{
			uuid: '61bf8fdf779b4406a61ddb0b91120acf',
			name: 'enableDrawingComponent',
			execute(context: IInitializationContext) {
				return import('@libs/productionplanning/item').then((module) => new module.PpsItemWizard().enableDrawingComponent(context));
			}
		},
		{
			uuid: 'cbf7e9c1569f4dfabfcc5845f0d03f13',
			name: 'disableDrawingComponent',
			execute(context: IInitializationContext) {
				return import('@libs/productionplanning/item').then((module) => new module.PpsItemWizard().disableDrawingComponent(context));
			}
		}
	];