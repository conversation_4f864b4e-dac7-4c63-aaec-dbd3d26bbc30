/*
 * Copyright(c) RIB Software GmbH
 */

import { Injectable } from '@angular/core';

import { DataServiceFlatRoot, ServiceRole, IDataServiceOptions, IDataServiceEndPointOptions, IDataServiceRoleOptions } from '@libs/platform/data-access';
import { IReportEntity, ReportComplete } from '../model/models';


@Injectable({
	providedIn: 'root',
})
export class ProductionplanningReportReportDataService extends DataServiceFlatRoot<IReportEntity, ReportComplete> {
	public constructor() {
		const options: IDataServiceOptions<IReportEntity> = {
			apiUrl: 'productionplanning/report/report',
			readInfo: <IDataServiceEndPointOptions>{
				endPoint: 'filtered',
				usePost: true,
			},
			deleteInfo: <IDataServiceEndPointOptions>{
				endPoint: 'multidelete',
			},
			updateInfo: <IDataServiceEndPointOptions>{
				endPoint: 'update',
			},
			roleInfo: <IDataServiceRoleOptions<IReportEntity>>{
				role: ServiceRole.Root,
				itemName: 'Reports',
			},
			entityActions: { createSupported: false, deleteSupported: true },
		};

		super(options);
	}
	public override createUpdateEntity(modified: IReportEntity | null): ReportComplete {
		const complete = new ReportComplete();
		if (modified !== null) {
			complete.MainItemId = modified.Id;
			complete.Reports = [modified];
		}

		return complete;
	}
}
