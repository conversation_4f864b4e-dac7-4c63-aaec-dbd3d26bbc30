/*
 * $Id$
 * Copyright(c) RIB Software GmbH
 */

export interface IQtoDetailPreviewResultReqGenerated {
	/*
	 * Factor
	 */
	Factor?: number | null;

	/*
	 * Goniometer
	 */
	Goniometer?: number | null;

	/*
	 * NoDecimals
	 */
	NoDecimals?: number | null;

	/*
	 * QtoFormulaFk
	 */
	QtoFormulaFk?: number | null;

	/*
	 * UseRoundedResults
	 */
	UseRoundedResults?: boolean | null;

	/*
	 * qtoDetails
	 */
	// qtoDetails?: IScriptQtoDetail[] | null;
}
