/*
 * Copyright(c) RIB Software GmbH
 */

import { HttpClient } from '@angular/common/http';
import { inject, Injectable, InjectionToken } from '@angular/core';
import { PlatformConfigurationService } from '@libs/platform/common';
import { BaseValidationService, IEntityRuntimeDataRegistry, IValidationFunctions, ValidationInfo, ValidationResult } from '@libs/platform/data-access';
import _ from 'lodash';
import { IMaterialNewEntity, IPpsMaterialCompEntity, IPpsMaterialEntity } from '../../model/models';
import { PpsMaterialCompatibilityDataService } from './pps-material-compatibility-data.service';

export const PPS_MATERIAL_COMPATIBILITY_VALIDATION_TOKEN = new InjectionToken<PpsMaterialCompatibilityValidationService>('ppsMaterialCompatibilityValidationToken');

@Injectable({
	providedIn: 'root'
})
export class PpsMaterialCompatibilityValidationService extends BaseValidationService<IPpsMaterialCompEntity> {

	private dataService = inject(PpsMaterialCompatibilityDataService);
	private http = inject(HttpClient);
	private configurationService = inject(PlatformConfigurationService);

	protected generateValidationFunctions(): IValidationFunctions<IPpsMaterialCompEntity> {
		return {
			MdcMaterialItemFk: this.validateMdcMaterialItemFk,
		};
	}

	protected getEntityRuntimeData(): IEntityRuntimeDataRegistry<IPpsMaterialCompEntity> {
		return this.dataService;
	}

	private async getOrCreatePpsMaterial(material: IMaterialNewEntity | undefined): Promise<IPpsMaterialEntity | null> {
		if (_.isNil(material)) {
			return null;
		}
		if (_.isNil(material.PpsMaterial)) {
			const resp = await this.http.get<IPpsMaterialEntity>(`${this.configurationService.webApiBaseUrl}productionplanning/ppsmaterial/getorcreateppsmaterial?mdcMaterialId=${material.Id}`).toPromise();
			if (!_.isNil(resp)) {
				material.PpsMaterial = resp;
			}
		}
		return material.PpsMaterial ?? null;
	}

	private validateMdcMaterialItemFk(info: ValidationInfo<IPpsMaterialCompEntity>): ValidationResult {
		const result = this.validateIsMandatory(info);
		// Additional logic here
		if (result.valid === true) {
			const selectedParent = this.dataService.getSelectedMaterial();
			this.getOrCreatePpsMaterial(selectedParent).then(ppsmaterial => {
				if (!_.isNil(ppsmaterial)) {
					info.entity.PpsMaterialProductFk = ppsmaterial.Id;
				}
			});

			const tmpMaterial = this.dataService.getMaterialById(info.value as number);
			this.getOrCreatePpsMaterial(tmpMaterial).then(ppsmaterial => {
				if (!_.isNil(ppsmaterial)) {
					info.entity.PpsMaterialItemFk = ppsmaterial.Id;
				}
			});
		}

		return result;
	}
}

// related ngjs code from \productionplanning.ppsmaterial\services\configurations\pps-material_compatibility-configuration.js
/*
let handlerFn = function (e, args) {
	let selected = productionplanningPpsMaterialRecordMainService.getSelected();
	if (selected) {
		if (_.isNil(selected.PpsMaterial)) {
			$http.get(globals.webApiBaseUrl + 'productionplanning/ppsmaterial/getorcreateppsmaterial?mdcMaterialId=' + selected.Id)
				.then(function (result){
					selected.PpsMaterial = result.data;
					args.entity.PpsMaterialProductFk = selected.PpsMaterial.Id;
				});
		} else {
			args.entity.PpsMaterialProductFk = selected.PpsMaterial.Id;
		}
	}

	let choice = args.selectedItem;
	if(choice){
		if (_.isNil(choice.PpsMaterial)) {
			$http.get(globals.webApiBaseUrl + 'productionplanning/ppsmaterial/getorcreateppsmaterial?mdcMaterialId=' + choice.Id)
				.then(function (result){
					choice.PpsMaterial = result.data;
					args.entity.PpsMaterialItemFk = choice.PpsMaterial.Id;
				});
		}
		else {
			args.entity.PpsMaterialItemFk = choice.PpsMaterial.Id;
		}
	}
};

*/