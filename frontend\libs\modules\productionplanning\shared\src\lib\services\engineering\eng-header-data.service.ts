import {
    DataServiceFlatNode,
    IDataServiceChildRoleOptions,
    IDataServiceEndPointOptions,
    IDataServiceOptions, IEntitySelection, ServiceRole
} from '@libs/platform/data-access';
import {IEngHeaderEntity} from '../../model/engineering/eng-header-entity.interface';
//import {Injectable} from '@angular/core';
import * as _ from 'lodash';
import {EngHeaderComplete} from '../../model/engineering/eng-header-complete.class';
import {get, set} from 'lodash';
import {IEntityIdentification, CompleteIdentification} from '@libs/platform/common';

// @Injectable({
//     providedIn: 'root'
// })

export class EngHeaderDataService<PT extends IEntityIdentification, PU extends CompleteIdentification<PT>> extends DataServiceFlatNode<IEngHeaderEntity,EngHeaderComplete, PT, PU> {
    public constructor(private parentService : IEntitySelection<PT>) {
        const options: IDataServiceOptions<IEngHeaderEntity> = {
            apiUrl: 'productionplanning/engineering/header',
            readInfo: <IDataServiceEndPointOptions> {
                endPoint: 'listbyproject',
                usePost: true,
                prepareParam: ident => {
                    return {
                        PKey1: ident.pKey1
                    };
                }
            },
            roleInfo: <IDataServiceChildRoleOptions<IEngHeaderEntity, PT, PU>>{
                role: ServiceRole.Node,
                itemName: 'EngHeader',
                parent: parentService
            }
        };
        super(options);
    }

    protected override onLoadSucceeded(loaded: object): IEngHeaderEntity[] {
        if (loaded) {
            return _.get(loaded, 'Main', []);
        }
        return [];
    }
    public override registerByMethod(): boolean {
        return true;
    }

    public override getSavedEntitiesFromUpdate(complete: PU): IEngHeaderEntity[] {
        const engHeaderToSave = get(complete, 'EngHeaderToSave') as EngHeaderComplete[];
        if (Array.isArray(engHeaderToSave)) {
            return engHeaderToSave.map(i => i.EngHeader);
        }
        return [];
    }

    public override registerNodeModificationsToParentUpdate(parentUpdate: PU, modified: EngHeaderComplete[], deleted: IEngHeaderEntity[]): void {
        if (modified && modified.some(() => true)) {
            set(parentUpdate, 'EngHeaderToSave', modified);
        }
        if (deleted && deleted.some(() => true)) {
            set(parentUpdate, 'EngHeaderToDelete', deleted);
        }
    }

    public override createUpdateEntity(modified: IEngHeaderEntity | null): EngHeaderComplete {
        const complete = new EngHeaderComplete();
        if (modified != null) {
            complete.MainItemId = modified.Id;
            complete.EngHeader = modified;
        }
        return complete;
    }
    public override isParentFn(parentKey: PT, entity: IEngHeaderEntity): boolean {
        return parentKey.Id === entity.ProjectFk;
    }
}
