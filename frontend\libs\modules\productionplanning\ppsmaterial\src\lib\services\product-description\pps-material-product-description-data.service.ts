// remark: current file is copied from basics-material-material-group-data.service in basics.material,
// should be replaced by other way(like <PERSON>zy<PERSON>n<PERSON>Token from basics.material module) in the future
/*
 * Copyright(c) RIB Software GmbH
 */

import { Injectable } from '@angular/core';
import { DataServiceFlatNode, IDataServiceChildRoleOptions, IDataServiceOptions, ServiceRole } from '@libs/platform/data-access';
import { PpsMaterialRecordDataService } from '../material/material-record-data.service';
import { PpsMaterialComplete } from '../../model/entities/pps-material-complete.class';
import { IMaterialNewEntity } from '../../model/entities/material-new-entity.interface';
import { IMdcProductDescriptionEntity } from '../../model/entities/mdc-product-description-entity.interface';
import { MdcProductDescriptionComplete } from '../../model/entities/mdc-product-description-complete.class';

/**
 * Material group data service
 */
@Injectable({
	providedIn: 'root',
})
export class PpsMaterialProductDescriptionDataService extends DataServiceFlatNode<IMdcProductDescriptionEntity, MdcProductDescriptionComplete, IMaterialNewEntity, PpsMaterialComplete> {
	/**
	 * The constructor
	 */
	public constructor(private readonly _parentService: PpsMaterialRecordDataService) {
		const options: IDataServiceOptions<IMdcProductDescriptionEntity> = {
			apiUrl: 'productionplanning/ppsmaterial/mdcproductdescription',
			roleInfo: <IDataServiceChildRoleOptions<IMdcProductDescriptionEntity, IMaterialNewEntity, PpsMaterialComplete>>{
				role: ServiceRole.Node,
				itemName: 'MdcProductDescription',
				parent: _parentService,
			},
			createInfo: {
				endPoint: 'create',
				usePost: true,
				prepareParam: (identity) => {
					return {
						Id: _parentService.getSelectedEntity()?.Id || -1,
					};
				},
			},
			readInfo: {
				endPoint: 'list',
				usePost: false,
				prepareParam: (ident) => {
					return { mainItemId: ident.pKey1 || -1 };
				},
			},
		};
		super(options);
	}
}
