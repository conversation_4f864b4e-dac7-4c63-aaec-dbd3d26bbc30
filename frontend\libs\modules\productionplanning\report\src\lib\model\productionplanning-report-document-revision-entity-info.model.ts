/*
 * Copyright(c) RIB Software GmbH
 */
 
import { EntityInfo } from '@libs/ui/business-base';
import { IPpsDocumentEntity, ProductionplanningSharedDocumentDataServiceManager, ProductionplanningShareDocumentRevisionEntityInfoFactory } from '@libs/productionplanning/shared';
import { IInitializationContext } from '@libs/platform/common';
import { ProductionplanningReportReportDataService } from '../services/productionplanning-report-report-data.service';


 export const PRODUCTIONPLANNING_REPORT_DOCUMENT_REVISION_ENTITY_INFO: EntityInfo =ProductionplanningShareDocumentRevisionEntityInfoFactory.create<IPpsDocumentEntity>({
	containerUuid: '57e6dd200e26447eaf69c2efd2e9ca97',
	permissionUuid: '5640a72648e24f21bf3985624c4d0fdf',
	gridTitle: { key: 'productionplanning.report.document.revision.listTitle' },
	parentServiceFn: (ctx) => {
		const parentOptions = {
			containerUuid: '72366c321e554a7d86da04e8fc996047',
			foreignKey: 'MntReportFk',
			parentServiceFn: (context: IInitializationContext) => context.injector.get(ProductionplanningReportReportDataService),
		};
		return ProductionplanningSharedDocumentDataServiceManager.getDataService(parentOptions, ctx);
                    
    }
});