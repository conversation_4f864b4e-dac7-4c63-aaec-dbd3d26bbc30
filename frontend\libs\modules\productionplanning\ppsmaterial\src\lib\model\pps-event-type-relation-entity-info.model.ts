/*
 * Copyright(c) RIB Software GmbH
 */

import { EntityInfo } from '@libs/ui/business-base';

import { PpsEventTypeRelationBehavior } from '../behaviors/pps-event-type-relation-behavior.service';
import { PpsEventTypeRelationDataService } from '../services/pps-event-type-relation/pps-event-type-relation-data.service';
import { PpsEventTypeRelationValidationService } from '../services/pps-event-type-relation/pps-event-type-relation-validation.service';
import { IPpsEventTypeRelEntity } from './models';
import { PPS_EVENT_TYPE_RELATION_LAYOUT } from './pps-event-type-relation-layout.model';

export const PPS_EVENT_TYPE_RELATION_ENTITY_INFO: EntityInfo = EntityInfo.create<IPpsEventTypeRelEntity>({
	grid: {
		title: { key: 'productionplanning.ppsmaterial.ppsEventTypeRelation.listViewTitle' },
		behavior: ctx => ctx.injector.get(PpsEventTypeRelationBehavior),
		containerUuid: '5ea20e4b3d0f40399bbf006633500b26'
	},
	form: {
		title: { key: 'productionplanning.ppsmaterial.ppsEventTypeRelation.detailViewTitle' },
		containerUuid: 'de0bbc30b6954aec9ed0abf0c66b4130',
	},
	dataService: ctx => ctx.injector.get(PpsEventTypeRelationDataService),
	validationService: ctx => ctx.injector.get(PpsEventTypeRelationValidationService),
	dtoSchemeId: { moduleSubModule: 'ProductionPlanning.PpsMaterial', typeName: 'PpsEventTypeRelDto' },
	permissionUuid: '5ea20e4b3d0f40399bbf006633500b26',
	layoutConfiguration: PPS_EVENT_TYPE_RELATION_LAYOUT,

});