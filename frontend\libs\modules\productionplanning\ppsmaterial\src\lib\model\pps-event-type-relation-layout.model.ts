/*
 * Copyright(c) RIB Software GmbH
 */

import { BasicsSharedLookupOverloadProvider } from '@libs/basics/shared';
import { prefixAllTranslationKeys, ServiceLocator } from '@libs/platform/common';
import { createLookup, FieldType, ILayoutConfiguration, UiCommonLookupDataFactoryService } from '@libs/ui/common';
import { IPpsEventTypeRelEntity } from './models';

export const PPS_EVENT_TYPE_RELATION_LAYOUT: ILayoutConfiguration<IPpsEventTypeRelEntity> = {
	groups: [
		{
			gid: 'basicConfiguration',
			attributes: ['PpsEventTypeParentFk', 'PpsEventTypeChildFk', 'RelationKindFk', 'CommentText', 'FixLagTime', 'FixDuration'],
		},
	],
	labels: {
		...prefixAllTranslationKeys('cloud.common.', {
			CommentText: { key: 'entityComment', text: '*Comment' },
		}),
		...prefixAllTranslationKeys('productionplanning.ppsmaterial.', {
			basicConfiguration: { key: 'ppsEventTypeRelation.basicConfiguration', text: '*Basic Configuration' },
			PpsEventTypeParentFk: { key: 'ppsEventTypeRelation.ppsEventTypeParentFk', text: '*Parent Event Type' },
			PpsEventTypeChildFk: { key: 'ppsEventTypeRelation.ppsEventTypeChildFk', text: '*Child Event Type' },
			RelationKindFk: { key: 'ppsEventTypeRelation.relationKindFk', text: '*Relation Kind' },
			FixLagTime: { key: 'ppsEventTypeRelation.fixLagTime', text: '*Fix Lag Time' },
			FixDuration: { key: 'ppsEventTypeRelation.fixDuration', text: '*Fix Duration' },
		}),

	},
	overloads: {
		PpsEventTypeParentFk: {
			type: FieldType.Lookup,
			lookupOptions: createLookup({
				dataService: ServiceLocator.injector.get(UiCommonLookupDataFactoryService).fromLookupType('EventType', {
					uuid: '062bcc5600454cc78b55d44548e532b5',
					valueMember: 'Id',
					displayMember: 'DescriptionInfo.Translated',
				}),
			}),
		},
		PpsEventTypeChildFk: {
			type: FieldType.Lookup,
			lookupOptions: createLookup({
				dataService: ServiceLocator.injector.get(UiCommonLookupDataFactoryService).fromLookupType('EventType', {
					uuid: '765f98d693e5422eb00950789d305fd5',
					valueMember: 'Id',
					displayMember: 'DescriptionInfo.Translated',
				}),
			}),
		},
		RelationKindFk: BasicsSharedLookupOverloadProvider.provideRelationKindLookupOverload(true),
		FixLagTime: {
			// disallowNegative: true
		},
		FixDuration: {
			// disallowNegative: true
		},
	}

};
