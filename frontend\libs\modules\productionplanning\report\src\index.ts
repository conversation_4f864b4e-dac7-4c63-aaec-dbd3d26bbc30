/*
 * Copyright(c) RIB Software GmbH
 */

import { IApplicationModuleInfo } from '@libs/platform/common';
import { ProductionplanningReportModuleInfo } from './lib/model/productionplanning-report-module-info.class';

export * from './lib/productionplanning-report.module';
export * from './lib/model/wizard/pps-report-wizard';

/**
 * Returns the module info object for the productionplanning report module.
 *
 * This function implements the {@link IApplicationModule.getModuleInfo} method.
 * Do not remove it.
 * It may be called by generated code.
 *
 * @return The singleton instance of the module info object.
 *
 * @see {@link IApplicationModule.getModuleInfo}
 */
export function getModuleInfo(): IApplicationModuleInfo {
	return ProductionplanningReportModuleInfo.instance;
}
