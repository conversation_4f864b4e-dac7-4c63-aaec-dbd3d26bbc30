{"name": "modules-productionplanning-report", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/modules/productionplanning/report/src", "projectType": "library", "targets": {"lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/modules/productionplanning/report/jest.config.ts"}}}}