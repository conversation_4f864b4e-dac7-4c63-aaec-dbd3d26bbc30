import { Injectable } from '@angular/core';
import { BasicsSharedSimpleActionWizardService, ISimpleActionOptions } from '@libs/basics/shared';

import { IEngDrawingComponentEntityGenerated } from '@libs/productionplanning/shared';
import {
	PpsItemProductTemplateComponentDataService
} from '../product-template/pps-item-product-template-component-data.service';


@Injectable({
	providedIn: 'root'
})
export class PpsItemDrawingComponentDisableWizardService extends BasicsSharedSimpleActionWizardService<IEngDrawingComponentEntityGenerated> {

	private readonly dataService = PpsItemProductTemplateComponentDataService.getInstance('a6293dfb6d944ae3a2c5a7ff3c55ed07');

	public onStartDisableWizard(): void {
		const option: ISimpleActionOptions<IEngDrawingComponentEntityGenerated> = {
			headerText: 'cloud.common.disableRecord',
			codeField: 'Description',
			doneMsg: 'productionplanning.item.wizard.drawingComponent.disableComponent',
			nothingToDoMsg: 'productionplanning.item.wizard.drawingComponent.componentAlreadyDisabled',
			questionMsg: 'cloud.common.questionDisableSelection'
		};
		this.startSimpleActionWizard(option);
	}

	public override filterToActionNeeded(selected: IEngDrawingComponentEntityGenerated[]): IEngDrawingComponentEntityGenerated[] {
		const filteredSelection: IEngDrawingComponentEntityGenerated[] = [];
		selected.forEach(item => {
			if (item.IsLive) {
				filteredSelection.push(item);
			}
		});
		return filteredSelection;
	}

	public override getSelection(): IEngDrawingComponentEntityGenerated[] {
		return this.dataService?.getSelection() || [];
	}

	public override performAction(filtered: IEngDrawingComponentEntityGenerated[]): void {
		filtered.forEach(item => {
			item.IsLive = false;
			this.dataService?.setModified(item);
		});
	}

	public override postProcess(): void {
	}

}