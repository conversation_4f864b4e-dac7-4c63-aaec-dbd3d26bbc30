/*
 * Copyright(c) RIB Software GmbH
 */

import { EntityInfo } from '@libs/ui/business-base';
import { ProductionplanningReportCostCodeDataService } from '../services/productionplanning-report-cost-code-data.service';
import { IReport2CostCodeEntity } from './models';
import { prefixAllTranslationKeys } from '@libs/platform/common';
import { BasicsSharedLookupOverloadProvider } from '@libs/basics/shared';
import { FieldType, IAdditionalLookupOptions, TypedConcreteFieldOverload } from '@libs/ui/common';
import { BASICS_COST_CODES_LOOKUP_PROVIDER_TOKEN, IBasicsCostCodeLookupProvider } from '@libs/basics/interfaces';
import { PlatformLazyInjectorService } from '@libs/platform/common';
import { IBasicsUomEntity } from '@libs/basics/interfaces';
import { PpsReportCostcodesValidationService } from '../services/validations/pps-report-costcodes-validation.service';

export const PRODUCTIONPLANNING_REPORT_COST_CODE_ENTITY_INFO: EntityInfo = EntityInfo.create<IReport2CostCodeEntity>({
	grid: {
		title: { text: 'Report: Additional Services' },
	},
	form: {
		title: { text: 'Additional Services Details' },
		containerUuid: '07632a49be5c46a88d8ba657cd61fd6b',
	},
	validationService: (ctx) => ctx.injector.get(PpsReportCostcodesValidationService),

	dataService: (ctx) => ctx.injector.get(ProductionplanningReportCostCodeDataService),
	dtoSchemeId: { moduleSubModule: 'ProductionPlanning.Report', typeName: 'Report2CostCodeDto' },
	permissionUuid: 'ed18f1fe891740b09bb464c8428e9239',
	layoutConfiguration: async (context) => {
		const lazyInjector = context.injector.get(PlatformLazyInjectorService);
		const basicsCostCodeProvider = await lazyInjector.inject(BASICS_COST_CODES_LOOKUP_PROVIDER_TOKEN);
		const uomLookupOverload = BasicsSharedLookupOverloadProvider.provideUoMLookupOverload(true);

		function costCodeLookupAndAdditionalFields(basicsCostCodeProvider: IBasicsCostCodeLookupProvider, descriptionColumnKey: string): TypedConcreteFieldOverload<IReport2CostCodeEntity> {
			return {
				...(basicsCostCodeProvider?.GenerateBasicsCostCodeLookup?.() ?? {}),
				additionalFields: [
					{
						displayMember: 'DescriptionInfo.Translated',
						label: {
							key: descriptionColumnKey,
						},
						column: true,
						singleRow: false,
					},
				],
			} as TypedConcreteFieldOverload<IReport2CostCodeEntity>;
		}

		return {
			groups: [
				{
					gid: 'baseGroup',
					attributes: ['CostCodeFk', 'Quantity', 'UomFk', 'CommentText'],
				},
			],
			overloads: {
				CostCodeFk: costCodeLookupAndAdditionalFields(basicsCostCodeProvider, 'productionplanning.report.report2costcode.costCodeDesc'),
				UomFk: {
					type: FieldType.Lookup,
					lookupOptions: (uomLookupOverload as IAdditionalLookupOptions<IBasicsUomEntity>).lookupOptions,
					additionalFields: [
						{
							id: 'DescriptionInfo.Translated',
							displayMember: 'DescriptionInfo.Translated',
							label: {
								text: '*UoM-Description',
								key: 'productionplanning.report.report2costcode.UomDescription',
							},
							column: true,
							singleRow: true,
						},
					],
				}
			},
			labels: {
				...prefixAllTranslationKeys('basics.costcodes.', {
					CostCodeFk: { key: 'costCodes' },
				}),
				...prefixAllTranslationKeys('cloud.common.', {
					Quantity: { key: 'entityQuantity' },
					UomFk: { key: 'entityUoM' },
					CommentText: { key: 'entityComment' },
				}),
			},
		};
	},
});
