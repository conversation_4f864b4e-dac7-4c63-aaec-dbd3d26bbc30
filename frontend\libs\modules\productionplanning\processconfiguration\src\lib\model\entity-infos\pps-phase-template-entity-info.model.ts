/*
 * Copyright(c) RIB Software GmbH
 */
import { EntityInfo } from '@libs/ui/business-base';
import { PhaseTemplateEntity, PPS_PROCESS_TEMPLATE_LOOKUP_PROVIDER_TOKEN, ProcessTemplateEntity } from '@libs/productionplanning/interfaces';
import { ProductionplanningProcessconfigurationPhaseTemplateDataService } from '../../services/productionplanning-processconfiguration-phase-template-data.service';
import { ProductionplanningProcessconfigurationPhaseTemplateGridBehavior } from '../../behaviors/phase-template-grid-behavior.service';
import { PpsPhaseTemplateValidationService } from '../../services/phase-template-validation.service';
import { DATESHIFT_MODES_TOKEN } from '@libs/productionplanning/shared';
import { BasicsSharedLookupOverloadProvider, BasicsSharedPpsProcessTypeLookupService } from '@libs/basics/shared';
import { FieldType, ILookupContext } from '@libs/ui/common';
import { prefixAllTranslationKeys } from '@libs/platform/common';

export const PPS_PHASE_TEMPLATE_ENTITY_INFO = EntityInfo.create<PhaseTemplateEntity>({
	grid: {
		title: { text: 'Phase Template', key: 'productionplanning.processconfiguration.phaseTemplate.ListTitle' },
		behavior: (ctx) => ctx.injector.get(ProductionplanningProcessconfigurationPhaseTemplateGridBehavior),
	},
	form: {
		title: {
			text: 'Phase Template Detail',
			key: 'productionplanning.processconfiguration.phaseTemplate.detailTitle',
		},
		containerUuid: '16109c4d63794f86812d51fbfe8eaedb',
	},
	dataService: (ctx) => ctx.injector.get(ProductionplanningProcessconfigurationPhaseTemplateDataService),
	validationService: (ctx) => ctx.injector.get(PpsPhaseTemplateValidationService),
	dtoSchemeId: { moduleSubModule: 'Productionplanning.ProcessConfiguration', typeName: 'PhaseTemplateDto' },
	permissionUuid: '71b79353b3084571b7b450a492a7fd56',
	layoutConfiguration: async (ctx) => {
		const dateShiftModes = ctx.injector.get(DATESHIFT_MODES_TOKEN);
		return {
			groups: [
				{
					gid: 'baseGroup',
					attributes: ['PhaseTypeFk', 'SequenceOrder', 'Duration', 'SuccessorLeadTime', 'PsdRelationkindFk', 'SuccessorMinSlackTime', 'DateshiftMode'],
				},
				{
					gid: 'placeHolder',
					attributes: ['IsPlaceholder', 'ProcessTemplateDefFk', 'ExecutionLimit'],
				},
			],
			overloads: {
				PhaseTypeFk: BasicsSharedLookupOverloadProvider.providePpsPhaseTypeLookupOverload(true),
				PsdRelationkindFk: BasicsSharedLookupOverloadProvider.provideRelationKindLookupOverload(true),
				DateshiftMode: {
					type: FieldType.Select,
					itemsSource: {
						items: dateShiftModes,
					},
				},
				ProcessTemplateDefFk: (await ctx.lazyInjector.inject(PPS_PROCESS_TEMPLATE_LOOKUP_PROVIDER_TOKEN)).provideDialogLookupOverload({
					clientSideAsyncFilter: {
						async execute(items: ProcessTemplateEntity[], context: ILookupContext<ProcessTemplateEntity, PhaseTemplateEntity>) {
							const types = await context.injector.get(BasicsSharedPpsProcessTypeLookupService).getListAsync();
							return items.filter((item) => types.find((type) => type.IsPlaceHolder && item.ProcessTypeFk === type.Id));
							//TODO: not sure how to deal with this case, need a way to replace basicsLookupdataLookupDescriptorService.getData -zwz
							/*
							var types = basicsLookupdataLookupDescriptorService.getData('basics.customize.ppsprocesstype');
							types = _.filter(types, function (type) {
								return type.IsPlaceHolder;
							});
							return !!_.find(types, {Id : entity.ProcessTypeFk});
							*/

							/* testing codes
							BasicsSharedPpsProcessTypeLookupService.getItemByKey
							IPpsProcessTemplateSimpleLookupEntity
							console.log(item.ProcessTypeFk)
							*/
							//return true;
						},
					},
				}),

				SuccessorLeadTime: {
					// disallowNegative: true
				},
				ExecutionLimit: {
					// disallowNegative: true
				},
			},
			labels: {
				...prefixAllTranslationKeys('cloud.common.', {
					baseGroup: { key: 'entityProperties', text: '*Basic Data' },
				}),

				...prefixAllTranslationKeys('productionplanning.common.', {
					DateshiftMode: { key: 'event.dateshiftMode' },
				}),

				...prefixAllTranslationKeys('productionplanning.processconfiguration.', {
					PhaseTypeFk: { key: 'phaseTemplate.phaseType' },
					SequenceOrder: { key: 'phaseTemplate.sequenceOrder' },
					Duration: { key: 'phaseTemplate.duration' },
					SuccessorLeadTime: { key: 'phaseTemplate.successorLeadTime' },
					PsdRelationkindFk: { key: 'phaseTemplate.psdRelationKind' },
					SuccessorMinSlackTime: { key: 'phaseTemplate.successorMinSlackTime' },
					placeHolder: { key: 'placeHolder' },
					IsPlaceholder: { key: 'phaseTemplate.isPlaceholder' },
					ProcessTemplateDefFk: { key: 'phaseTemplate.defaultProcessTemplate' },
					ExecutionLimit: { key: 'phaseTemplate.executionLimit' },
				}),
			},
		};
	},
});
