/*
 * Copyright(c) RIB Software GmbH
 */

import { CompleteIdentification } from '@libs/platform/common';

import { IPpsProductEntityGenerated } from '@libs/productionplanning/shared';

export class Report2ProductComplete implements CompleteIdentification<IPpsProductEntityGenerated> {
	/**
	 * MainItemId
	 */
	public MainItemId: number = 0;

	/**
	 * Report2Product
	 */
	public Report2Product?: IPpsProductEntityGenerated[] | null = [];
}
