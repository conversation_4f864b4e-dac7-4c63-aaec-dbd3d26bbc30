/*
 * Copyright(c) RIB Software GmbH
 */
import { IInitializationContext } from '@libs/platform/common';
import { PpsChangeReportStatusService } from '../../services/wizards/pps-change-report-status.service';

export class PpsReportWizard {
    public changeReportStatus(context: IInitializationContext) {
        const service = context.injector.get(PpsChangeReportStatusService);
        service.onStartChangeStatusWizard();
    }
}