/*
 * Copyright(c) RIB Software GmbH
 */
import { inject, Injectable } from '@angular/core';
import {
    BasicsSharedCustomizeLookupOverloadProvider,
    BasicsSharedLookupOverloadProvider
} from '@libs/basics/shared';

import { prefixAllTranslationKeys } from '@libs/platform/common';
import {createLookup, FieldType, UiCommonLookupDataFactoryService} from '@libs/ui/common';
import {ProjectSharedLookupService} from '@libs/project/shared';

/**
 * Eng Header layout service
 */
@Injectable({
    providedIn: 'root',
})
export class EngHeaderLayoutService {
    private lookupServiceFactory = inject(UiCommonLookupDataFactoryService);
    /**
     * Generate layout config
     */
    public generateLayout()  {
        return {
            groups: [
                {
                    gid: 'basicData',
                    attributes: ['EngStatusFk', 'Code', 'Description', 'ProjectFk', 'EngTypeFk', 'LgmJobFk', 'Remark', 'IsLive']
                },
                {
                    gid: 'planningInfoGroup',
                    attributes: [
                        'ClerkFk', 'ModelFk'
                    ]
                },
            ],
            labels: {
                ...prefixAllTranslationKeys('cloud.common.', {
                    basicData: { key: 'entityProperties' },
                    EngStatusFk: { key: 'entityStatus' },
                    Code: {key: 'entityCode'},
                    Description: {key: 'entityDescription'},
                    ProjectFk: {key: 'entityProjectNo'},
                    Remark: {key: 'entityRemarks'},
                }),
                ...prefixAllTranslationKeys('project.costcodes.', {
                    LgmJobFk: {key: 'lgmJobFk'}
                }),
                ...prefixAllTranslationKeys('basics.customize.', {
                    IsLive: {key: 'islive'}
                }),
                ...prefixAllTranslationKeys('productionplanning.engineering.', {
                    EngTypeFk: { key: 'entityEngTypeFk' },
                    ClerkFk: {key: 'entityClerkFk'}
                }),
                ...prefixAllTranslationKeys('productionplanning.common.', {
                    planningInfoGroup: { key: 'event.planInformation' },
                }),
                ...prefixAllTranslationKeys('model.main.', {
                    ModelFk: { key: 'entityModel' },
                }),
            },
            overloads: {
                ClerkFk: BasicsSharedLookupOverloadProvider.providerBasicsClerkLookupOverload(true),
                EngStatusFk: BasicsSharedCustomizeLookupOverloadProvider.provideEngineeringStatusReadonlyLookupOverload(),
                ProjectFk: {
                    type: FieldType.Lookup,
                    visible: true,
                    lookupOptions: createLookup({
                        dataServiceToken: ProjectSharedLookupService,
                        showDescription: true,
                        descriptionMember: 'ProjectNo',
                    }),
                },
                EngTypeFk: BasicsSharedCustomizeLookupOverloadProvider.provideEngineeringTypeLookupOverload(false),
            }
        };
    }
}
