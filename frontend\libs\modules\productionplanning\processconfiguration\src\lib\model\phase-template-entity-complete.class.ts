/*
 * Copyright(c) RIB Software GmbH
 */
/* tslint:disable */

import { PhaseRequirementTemplateEntity, PhaseTemplateEntity } from '@libs/productionplanning/interfaces';
import { CompleteIdentification } from '@libs/platform/common';

export class PhaseTemplateEntityComplete implements CompleteIdentification<PhaseTemplateEntity> {
	public MainItemId: number = 0;
	public PhaseTemplate: PhaseTemplateEntity | null = null;
	public PhaseTemplates: PhaseTemplateEntity[] | null = [];
	public PhaseReqTemplateToDelete?: PhaseRequirementTemplateEntity[] = [];
	public PhaseReqTemplateToSave?: PhaseRequirementTemplateEntity[] = [];
}
