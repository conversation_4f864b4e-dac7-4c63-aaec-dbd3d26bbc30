import { inject, Injectable } from '@angular/core';
import {
	FieldType,
	IAdditionalLookupOptions, IEditorDialogResult,
	IFormConfig,
	StandardDialogButtonId,
	UiCommonFormDialogService
} from '@libs/ui/common';
import { BasicsSharedLookupOverloadProvider } from '@libs/basics/shared';
import { PpsItemProductDataService } from './pps-item-product-data.service';
import { IPpsProductEntityGenerated } from '@libs/productionplanning/shared';
import { IPPSItemEntity } from '@libs/productionplanning/common';

interface IProductManualCreationDialogEntity {
	EndDate: Date | null,
	ProductionPlaceFk: number | null
}

interface ICreationConfig {
	subPuSiteChildrenIds: number[],
	mandatory: boolean,
}


@Injectable({
	providedIn: 'root'
})
export class PpsItemProductManualCreationDialogService {

	private formDialogService: UiCommonFormDialogService;
	private dialogEntity: IProductManualCreationDialogEntity;

	public constructor(private productService: PpsItemProductDataService) {
		this.formDialogService = inject(UiCommonFormDialogService);
		this.dialogEntity = {
			EndDate: null,
			ProductionPlaceFk: null
		};
	}

	public async openCreationDialog(config: ICreationConfig, parentItem: IPPSItemEntity) {
			let newCreationProduct :IPpsProductEntityGenerated;
			this.productService.create().then(product => {
			product.ProductionSetFk = parentItem.ProductionSetId;
			product.ItemFk = parentItem.Id;
			product.ProductDescriptionFk = parentItem.ProductDescriptionFk!;
			product.LgmJobFk = parentItem.LgmJobFk;
			product.SiteFks = config.subPuSiteChildrenIds;
			product.Code =	'Is Generated'; // just for passing required validation of .net Core validator on ProductDto when posting data to the server, product's code will be auto-generated in server side
			newCreationProduct = product;
			this.productService.validationDatashift(product, this.dialogEntity.EndDate!, true);
		});

		await this.formDialogService.showDialog<IProductManualCreationDialogEntity>({
			id: 'creationDialog',
			headerText: { key: 'cloud.common.taskBarNewRecord' },
			formConfiguration: this.checkingDialogFormConfig,
			entity: this.dialogEntity,
			runtime: undefined,
			customButtons: [],
			topDescription: '',
			width: '800px',
			maxHeight: '600px'
		})?.then(result => {
			if (result?.closingButtonId === StandardDialogButtonId.Ok) {
				this.handleOk(result, newCreationProduct);
			} else {
				this.handleCancel(result);
			}
		});
	}

	private readonly ProductionPlaceConfig = BasicsSharedLookupOverloadProvider.providePpsProductPlaceTypeLookupOverload(true);

	private checkingDialogFormConfig: IFormConfig<IProductManualCreationDialogEntity> = {
		formId: 'checking-dialog-form',
		showGrouping: false,
		rows: [{
			id: 'date',
			label: {
				key: 'cloud.common.entityDate', text: '*Date'
			},
			type: FieldType.Date,
			model: 'EndDate',
		}, {
			id: 'productionPlaceFk',
			label: {
				key: 'productionplanning.processconfiguration.phase.PpsProdPlaceFk', text: '*Production Place'
			},
			type: FieldType.Lookup,
			model: 'ProductionPlaceFk',
			readonly: true,
			lookupOptions: (this.ProductionPlaceConfig as IAdditionalLookupOptions<IProductManualCreationDialogEntity>).lookupOptions,
		},
		],
	};

	private handleOk(result: IEditorDialogResult<IProductManualCreationDialogEntity>, newCreationProduct: IPpsProductEntityGenerated) {
		const productList = this.productService.getList();
		productList.push(newCreationProduct);

		this.productService.setList(productList);
		this.productService.entitiesUpdated(productList);
	}

	private handleCancel(result: IEditorDialogResult<IProductManualCreationDialogEntity>) {
	}
}