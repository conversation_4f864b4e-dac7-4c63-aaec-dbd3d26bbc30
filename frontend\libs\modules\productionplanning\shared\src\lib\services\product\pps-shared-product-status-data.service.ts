import { inject, Injectable } from '@angular/core';

import { IBasicsCustomizePpsProductStatusEntity } from '@libs/basics/interfaces';
import _ from 'lodash';
import { PlatformConfigurationService, PlatformHttpService } from '@libs/platform/common';

import { IPpsProductEntityGenerated } from '../../model/product/product-entity-generated.interface';

@Injectable({
	providedIn: 'root',
})
export class PpsSharedProductStatusDataService{

	private https = inject(PlatformHttpService);
	private configurationService = inject(PlatformConfigurationService);

	private productStatusList: IBasicsCustomizePpsProductStatusEntity[] | null;

	public constructor() {
		this.productStatusList = [];
		this.load();
	}

	public getProductList():IBasicsCustomizePpsProductStatusEntity[] | null {
		return this.productStatusList;
	}

	public allowProductToBeDeleted(list: IPpsProductEntityGenerated[]): boolean {
		let deletableProducts = [];
		if(list && list.length > 0) {
			const deletableStatusList = _.filter(this.productStatusList, (status) => {
				return status.IsDeletable;
			});
			if (deletableStatusList.length > 0) {
				const deletableStatusIds = _.map(deletableStatusList, 'Id');
				deletableProducts = _.filter(list, (product) => {
					return deletableStatusIds.indexOf(product.ProductStatusFk) > -1;
				});
			}
		}
		return deletableProducts.length === list.length;
	}

	public clearCache() {
		this.productStatusList = [];
	}

	public async load(){
		this.https.post('basics/customize/ppsproductstatus/list', {}).then((response) => {
			if (response) {
				this.productStatusList = response as IBasicsCustomizePpsProductStatusEntity[];
				//basicsLookupdataLookupDescriptorService.updateData('basics.customize.ppsproductstatus', response.data);
			}
		});
	}
}