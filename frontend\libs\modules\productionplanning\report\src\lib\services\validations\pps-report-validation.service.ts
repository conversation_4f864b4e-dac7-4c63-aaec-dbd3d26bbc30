/*
 * Copyright(c) RIB Software GmbH
 */
import { BaseValidationService, IEntityRuntimeDataRegistry, IValidationFunctions, ValidationInfo, ValidationResult } from '@libs/platform/data-access';
import { inject, Injectable } from '@angular/core';
import { IReportEntity } from '../../model/models';
import { ProductionplanningReportReportDataService } from '../productionplanning-report-report-data.service';
import { HttpClient } from '@angular/common/http';
import { BasicsSharedDataValidationService } from '@libs/basics/shared';
import { PlatformTranslateService } from '@libs/platform/common';
@Injectable({
	providedIn: 'root',
})
export class PpsReportValidationService extends BaseValidationService<IReportEntity> {
	private dataService = inject(ProductionplanningReportReportDataService);
	protected http = inject(HttpClient);
	private readonly validationUtils = inject(BasicsSharedDataValidationService);
	private readonly translateService = inject(PlatformTranslateService);

	protected generateValidationFunctions(): IValidationFunctions<IReportEntity> {
		return {
			Code: this.validationCode,
			StartTime: this.validateStartTime,
			FinishTime: this.validateFinishTime,
		};
	}
	protected getEntityRuntimeData(): IEntityRuntimeDataRegistry<IReportEntity> {
		return this.dataService;
	}
	private async validationCode(info: ValidationInfo<IReportEntity>): Promise<ValidationResult> {
		return this.validationUtils.isSynAndAsyncUnique(info, this.dataService.getList(), 'productionplanning/report/report/isuniquecode');
	}
	private validateStartTime(info: ValidationInfo<IReportEntity>): ValidationResult {
		return this.validationUtils.validatePeriod(this.getEntityRuntimeData(), info, <string>info.value, info.entity.StartTime ? info.entity.StartTime.toString() : '', 'EndTime');
	}
	private validateFinishTime(info: ValidationInfo<IReportEntity>): ValidationResult {
		return this.validationUtils.validatePeriod(this.getEntityRuntimeData(), info, <string>info.value, info.entity.EndTime ? info.entity.EndTime.toString() : '', 'StartTime');
	}
}
