/*
 * Copyright(c) RIB Software GmbH
 */

import {inject, Injectable} from '@angular/core';
import {BasicsSharedChangeStatusService, IStatusChangeOptions} from '@libs/basics/shared';

import { PpsItemDataService } from '../../services/pps-item-data.service';
import { IPPSItemEntity } from '@libs/productionplanning/common';
import { PPSItemComplete } from '../models';


@Injectable({
	providedIn: 'root'
})
export class PpsItemChangeStatusWizardService extends BasicsSharedChangeStatusService<IPPSItemEntity, IPPSItemEntity, PPSItemComplete> {
	protected readonly dataService = inject(PpsItemDataService);

	protected statusConfiguration: IStatusChangeOptions<IPPSItemEntity, PPSItemComplete> = {
		title: 'productionplanning.item.wizard.changeItemStatus',
		guid: '0bd0c22574f841b4a907de00e5af3f46',
		isSimpleStatus: true,
		statusName: 'ppsitem',
		checkAccessRight: true,
		statusField: 'PPSItemStatusFk',
		updateUrl: 'productionplanning/item/wizard/changeitemstatus',
		rootDataService: this.dataService
	};

	public onStartChangeStatusWizard() {
		this.startChangeStatusWizard();
	}

	public override afterStatusChanged() {
		this.dataService.refreshSelected ? this.dataService.refreshSelected() : this.dataService.refreshAll();
	}
}