{"name": "modules-qto-common", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/modules/qto/common/src", "prefix": "qto-common", "tags": [], "projectType": "library", "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/modules/qto/common/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}}}