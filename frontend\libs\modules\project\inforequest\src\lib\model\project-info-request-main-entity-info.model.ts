/*
 * $Id$
 * Copyright(c) RIB Software GmbH
 */

import { prefixAllTranslationKeys } from '@libs/platform/common';
import { createLookup, FieldType, ILayoutConfiguration } from '@libs/ui/common';

import { ProjectInfoRequestDataService } from '../services/project-info-request-data.service';
import { ProjectInfoRequestValidationService } from '../services/project-info-request-data-validation.service';
import { IEntityInfo, EntityInfo } from '@libs/ui/business-base';

import { IProjectInfoRequestEntity } from '@libs/project/interfaces';
import { BUSINESSPARTNER_RELATED_LOOKUP_PROVIDER_TOKEN } from '@libs/businesspartner/interfaces';
import { BASICS_CUSTOMIZE_LOOKUP_PROVIDER_TOKEN } from '@libs/basics/interfaces';
//import { MODEL_LOOKUP_PROVIDER_TOKEN } from '@libs/model/interfaces';

import { ProjectSharedLookupOverloadProvider } from '@libs/project/shared';
import { BasicsCompanyLookupService, BasicsSharedLookupOverloadProvider } from '@libs/basics/shared';
import { ProcurementShareContractLookupService } from '@libs/procurement/shared';



export const PROJECT_INFO_REQUEST_MAIN_ENTITY_INFO: EntityInfo = EntityInfo.create({
	grid: {
		title: {
			text: '',
			key: 'project.inforequest.projectInfoRequestListTitle'
		},
	},
	form: {
		title: {
			text: '',
			key: 'project.inforequest.projectInfoRequestDetailTitle'
		},
		containerUuid: '8b9c47c94f0b4077beaaab998c399048'
	},
	dataService: (ctx) => ctx.injector.get(ProjectInfoRequestDataService),
	validationService: (ctx) => ctx.injector.get(ProjectInfoRequestValidationService),
	dtoSchemeId: {
		moduleSubModule: 'Project.InfoRequest',
		typeName: 'InfoRequestDto'
	},
	permissionUuid: '281de48b068c443c9b7c62a7f51ac45f',
	layoutConfiguration: async (ctx) => {
		const bpRelatedLookupProvider = await ctx.lazyInjector.inject(BUSINESSPARTNER_RELATED_LOOKUP_PROVIDER_TOKEN);
		//const modelLookupProvider = await ctx.lazyInjector.inject(MODEL_LOOKUP_PROVIDER_TOKEN);
		const customizeLookupProvider = await ctx.lazyInjector.inject(BASICS_CUSTOMIZE_LOOKUP_PROVIDER_TOKEN);
		return <ILayoutConfiguration<IProjectInfoRequestEntity>>{
			groups: [
				{
					gid: 'default',
					attributes: [
						'Code',
						'Description',
						'ProjectFk',
						'ModelFk',
						'RequestStatusFk',
						'RequestGroupFk',
						'RequestTypeFk',
						'Specification',
						'ClerkRaisedByFk',
						'ClerkResponsibleFk',
						'ClerkCurrentFk',
						'BusinesspartnerFk',
						'ContactFk',
						'DateRaised',
						'DateDue',
						'DefectFk',
						'ChangeFk',
						'SearchPattern',
						'CompanyFk',
						'Rfi2DefectTypeFk',
						'Rfi2ChangeTypeFk',
						'RubricCategoryFk',
						'HeaderFk',
						'Remark',
						'PriorityFk',
						'SubsidiaryFk',
						'UserDefined1',
						'UserDefined2',
						'UserDefined3',
						'UserDefined4',
						'UserDefined5',
					]
				},
			],
			overloads: {
				ProjectFk: ProjectSharedLookupOverloadProvider.provideProjectLookupOverload(false),
				//ModelFk: modelLookupProvider.generateModelLookup(), // ToDo: lookup still incorrect
				RequestStatusFk: customizeLookupProvider.provideRfIStatusLookupOverload(),
				RequestGroupFk: customizeLookupProvider.provideRfIGroupLookupOverload(),
				RequestTypeFk: customizeLookupProvider.provideRfITypeLookupOverload(),
				ClerkRaisedByFk: BasicsSharedLookupOverloadProvider.providerBasicsClerkLookupOverload(true, 'project.inforequest.entityClerkRaisedByDescription'),
				ClerkResponsibleFk: BasicsSharedLookupOverloadProvider.providerBasicsClerkLookupOverload(true, 'project.inforequest.entityClerkResponsibleDescription'),
				ClerkCurrentFk: BasicsSharedLookupOverloadProvider.providerBasicsClerkLookupOverload(true, 'project.inforequest.entityClerkCurrentDescription'),
				BusinesspartnerFk: bpRelatedLookupProvider.getBusinessPartnerLookupOverload({
					showClearButton: true,
				}),
				SubsidiaryFk: bpRelatedLookupProvider.getSubsidiaryLookupOverload({  //ToDo: Bug https://rib-40.atlassian.net/browse/DEV-41939
					showClearButton: true,
					serverFilterKey: 'project-info-reques-subsidiary-filter',
					restrictToBusinessPartners: (entity) => (entity as IProjectInfoRequestEntity).BusinesspartnerFk
				}),
				ContactFk: bpRelatedLookupProvider.getContactLookupOverload({
					showClearButton: true,
					serverFilterKey: 'project-info-request-contact-by-bizpartner-filter',
					restrictToBusinessPartners: (entity) => (entity as IProjectInfoRequestEntity).BusinesspartnerFk
				}),
				CompanyFk: {
					type: FieldType.Lookup,
					lookupOptions: createLookup({
						dataServiceToken: BasicsCompanyLookupService,
						showDescription: true,
						descriptionMember: 'CompanyName'
					})
				},
				Rfi2DefectTypeFk: customizeLookupProvider.provideRequestForInfo2DefectTypeLookupOverload(),
				Rfi2ChangeTypeFk: customizeLookupProvider.provideRequestForInfo2ProjectChangeTypeLookupOverload(),
				RubricCategoryFk: customizeLookupProvider.provideRubricCategoryLookupOverload(),
				HeaderFk: {
					type: FieldType.Lookup,
					lookupOptions: createLookup({
						dataServiceToken: ProcurementShareContractLookupService,
						showDescription: true,
						descriptionMember: 'Description',
					}),
				},
				PriorityFk: customizeLookupProvider.providePriorityLookupOverload(),
				DefectFk:{
					type: FieldType.Text,
					readonly: true,
				},
				ChangeFk: {
					type: FieldType.Text,
					readonly: true,
				}
			},
			labels: {
				...prefixAllTranslationKeys('project.inforequest.', {
					ModelFk: { key: 'entityModelFk' },
					MarkerFk: { key: 'entityMarkerFk' },
					ObjectSetFk: { key: 'entityObjectSetFk' },
					RequestStatusFk: { key: 'entityRequestStatusFk' },
					RequestGroupFk: { key: 'entityRequestGroupFk' },
					RequestTypeFk: { key: 'entityRequestTypeFk' },
					ClerkRaisedByFk: { key: 'entityClerkRaisedByFk' },
					ClerkResponsibleFk: { key: 'entityClerkResponsibleFk' },
					ClerkCurrentFk: { key: 'entityClerkCurrentFk' },
					DateRaised: { key: 'entityDateRaised' },
					DateDue: { key: 'entityDateDue' },
					Rfi2DefectTypeFk: { key: 'entityRfi2DefectTypeFk' },
					Rfi2ChangeTypeFk: { key: 'entityRfi2ChangeTypeFk' },
					PriorityFk: { key: 'entityPriorityFk' },
					DefectFk: { key: 'defectFk' },
					ChangeFk:{ key: 'changeFk' }
				}),
				...prefixAllTranslationKeys('cloud.common.', {
					Code: { key: 'entityCode' },
					Description: { key: 'entityDescription' },
					ProjectFk: { key: 'entityProjectName' },
					Specification: { key: 'EntitySpec' },
					SearchPattern: { key: 'entitySearchPattern' },
					CompanyFk: { key: 'entityCompany' },
					Remark: { key: 'entityRemark' },
					UserDefined1: { key: 'entityUserDefined', params: { p_0: '1' } },
					UserDefined2: { key: 'entityUserDefined', params: { p_0: '2' } },
					UserDefined3: { key: 'entityUserDefined', params: { p_0: '3' } },
					UserDefined4: { key: 'entityUserDefined', params: { p_0: '4' } },
					UserDefined5: { key: 'entityUserDefined', params: { p_0: '5' } },
					SubsidiaryFk: { key: 'entitySubsidiary' },
					HeaderFk: { key: 'entityContract' }
				}),
				...prefixAllTranslationKeys('sales.contract.', {
					BusinesspartnerFk: { key: 'entityBusinesspartnerFk' },
					ContactFk: { key: 'entityContactFk' }
				}),
				...prefixAllTranslationKeys('basics.customize.', {
					RubricCategoryFk: { key: 'rubriccategoryfk' }
				})
			}
		};
	}
} as IEntityInfo<IProjectInfoRequestEntity>);