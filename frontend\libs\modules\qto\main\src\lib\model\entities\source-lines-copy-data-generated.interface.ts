/*
 * $Id$
 * Copyright(c) RIB Software GmbH
 */

export interface ISourceLinesCopyDataGenerated {
	/*
	 * BilHeaderFk
	 */
	BilHeaderFk?: number | null;

	/*
	 * BoqItem
	 */
	// BoqItem?: IBoqItemEntity | null;

	/*
	 * IsAssetMaster
	 */
	IsAssetMaster?: boolean | null;

	/*
	 * IsBillTo
	 */
	IsBillTo?: boolean | null;

	/*
	 * IsContract
	 */
	IsContract?: boolean | null;

	/*
	 * IsControllingUnit
	 */
	IsControllingUnit?: boolean | null;

	/*
	 * IsCostGroup
	 */
	IsCostGroup?: boolean | null;

	/*
	 * IsLocation
	 */
	IsLocation?: boolean | null;

	/*
	 * IsOneProject
	 */
	IsOneProject?: boolean | null;

	/*
	 * IsPrc
	 */
	IsPrc?: boolean | null;

	/*
	 * IsSortCode
	 */
	IsSortCode?: boolean | null;

	/*
	 * ModuleName
	 */
	ModuleName?: string | null;

	/*
	 * PesHeaderFk
	 */
	PesHeaderFk?: number | null;

	/*
	 * QtoLineIds
	 */
	QtoLineIds?: number[] | null;

	/*
	 * SourceQtoHeaderFk
	 */
	SourceQtoHeaderFk?: number | null;

	/*
	 * TagetQtoHeaderFk
	 */
	TagetQtoHeaderFk?: number | null;

	/*
	 * WipHeaderFk
	 */
	WipHeaderFk?: number | null;
}
