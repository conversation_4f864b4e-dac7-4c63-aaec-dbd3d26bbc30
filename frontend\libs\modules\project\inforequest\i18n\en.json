{"project": {"inforequest": {"projectInfoRequestListTitle": "Requests for Information", "projectInfoRequestDetailTitle": "Request for Information Details", "projectInfoRequest2ExternalListTitle": "Requests for Information to External", "projectInfoRequest2ExternalDetailTitle": "Request for Information to External Details", "projectInfoRequestReferenceListTitle": "References", "projectInfoRequestReferenceDetailTitle": "Reference Details", "projectInfoRequestRelevantToListTitle": "Relevant To", "projectInfoRequestRelevantToDetailTitle": "Relevant To Details", "entityExtGuid": "External Guid", "entityExtName": "External Name", "entityExtPath": "External Path", "entityExternalSourceFk": "External Source", "entityInfoRequestFk": "Info Request", "entityRequestFromFk": "Request From", "entityRequestToFk": "Request To", "entityReferenceTypeFk": "Reference Type", "entityModelFk": "Model", "entityMarkerFk": "<PERSON><PERSON>", "ObjectSetFk": "Object Set", "entityRequestStatusFk": "Request Status", "entityRequestTypeFk": "Request Type", "entityClerkRaisedByFk": "Clerk Raised By", "entityClerkResponsibleFk": "Clerk Responsible", "entityDateRaised": "Date Raised", "entityDateDue": "Date Due", "entityRfi2DefectTypeFk": "Rfi To Defect Type", "entityRfi2ChangeTypeFk": "Rfi To Change Type", "entityHeaderFk": "Header", "entityPriorityFk": "Priority", "entityObjectSetFk": "Object Set", "entityClerkCurrentFk": "Clerk Current", "entityRequestGroupFk": "Request Group", "defectFk": "Defect", "changeFk": "Change", "entityClerkCurrentDescription": "Clerk Current Description", "entityClerkRaisedByDescription": "Clerk Raised By Description", "entityClerkResponsibleDescription": "Clerk Responsible Description", "bim360RFIs": {"syncRFITitle": "Synchronize BIM 360 RFIs to iTWO 4.0", "btnLoadRFIs": "Load RFIs", "chooseRFIText": "Choose RFIs:", "isSelectedTitle": "Selected", "projectNameText": "Project Name", "filterStatusText": "Status", "filterShowImportedText": "Show Imported RFIs", "status_all": "(All)", "status_draft": "DRAFT", "status_submitted": "SUBMITTED", "status_open": "OPEN", "status_answered": "ANSWERED", "status_rejected": "REJECTED", "status_closed": "CLOSED", "status_void": "VOID", "importDocumentText": "Import Documents", "RFIsNotFoundInBim360": "RFIs not found in BIM 360.", "RFIsSaved": "Selected RFIs have been saved successfully.", "RFIsNotSaved": "Failed to save the selected RFIs. Message:", "columns": {"title": "Title", "status": "Status", "description": "Description", "dueDate": "Due Date", "assignedTo": "Assigned To"}}}}}