/*
 * $Id$
 * Copyright(c) RIB Software GmbH
 */

export interface IQtoDetailReadEntityGenerated {
	/*
	 * BasRubricCategoryFk
	 */
	BasRubricCategoryFk?: number | null;

	/*
	 * BilHeaderFk
	 */
	BilHeaderFk?: number | null;

	/*
	 * BillTos
	 */
	BillTos?: number[] | null;

	/*
	 * BoqHeaderFk
	 */
	BoqHeaderFk?: number | null;

	/*
	 * BoqSplitQuantityFk
	 */
	BoqSplitQuantityFk?: number | null;

	/*
	 * Boqs
	 */
	Boqs?: number[] | null;

	/*
	 * CostGroupFks
	 */
	CostGroupFks?: number[] | null;

	/*
	 * IsBillingBoq
	 */
	IsBillingBoq?: boolean | null;

	/*
	 * IsCrbBoq
	 */
	IsCrbBoq?: boolean | null;

	/*
	 * IsFilterByNoWipOrBilActive
	 */
	IsFilterByNoWipOrBilActive?: boolean | null;

	/*
	 * IsPesBoq
	 */
	IsPesBoq?: boolean | null;

	/*
	 * IsPrcBoq
	 */
	IsPrcBoq?: boolean | null;

	/*
	 * IsPrjBoq
	 */
	IsPrjBoq?: boolean | null;

	/*
	 * IsQtoBoq
	 */
	IsQtoBoq?: boolean | null;

	/*
	 * IsWipBoq
	 */
	IsWipBoq?: boolean | null;

	/*
	 * Locations
	 */
	Locations?: number[] | null;

	/*
	 * MainItemId
	 */
	MainItemId?: number | null;

	/*
	 * PageNumberIds
	 */
	PageNumberIds?: number[] | null;

	/*
	 * PesHeaderFk
	 */
	PesHeaderFk?: number | null;

	/*
	 * PrjProjectFk
	 */
	PrjProjectFk?: number | null;

	/*
	 * QtoDetailIds
	 */
	QtoDetailIds?: number[] | null;

	/*
	 * Structures
	 */
	Structures?: number[] | null;

	/*
	 * SubQuantityBoqItemFks
	 */
	SubQuantityBoqItemFks?: number[] | null;

	/*
	 * WipHeaderFk
	 */
	WipHeaderFk?: number | null;
}
