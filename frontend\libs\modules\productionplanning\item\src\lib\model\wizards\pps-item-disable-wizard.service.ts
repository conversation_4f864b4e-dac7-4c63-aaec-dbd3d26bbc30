
import { inject, Injectable } from '@angular/core';
import { BasicsSharedSimpleActionWizardService, ISimpleActionOptions } from '@libs/basics/shared';
import { IPPSItemEntity } from '@libs/productionplanning/common';
import { PpsItemDataService } from '../../services/pps-item-data.service';


@Injectable({
	providedIn: 'root'
})
export class PpsItemDisableWizardService extends BasicsSharedSimpleActionWizardService<IPPSItemEntity> {

	private readonly ppsItemDataService = inject(PpsItemDataService);

	public onStartDisableWizard(): void {
		const option: ISimpleActionOptions<IPPSItemEntity> = {
			headerText: 'cloud.common.disableRecord',
			codeField: 'Code',
			doneMsg: 'productionplanning.item.wizard.enableDisableItemDone',
			nothingToDoMsg: 'productionplanning.item.wizard.itemAlreadyDisabled',
			questionMsg: 'cloud.common.questionDisableSelection'
		};
		this.startSimpleActionWizard(option);
	}

	public override filterToActionNeeded(selected: IPPSItemEntity[]): IPPSItemEntity[] {
		const filteredSelection: IPPSItemEntity[] = [];
		selected.forEach(item => {
			if (item.IsLive) {
				filteredSelection.push(item);
			}
		});
		return filteredSelection;
	}

	public override getSelection(): IPPSItemEntity[] {
		return this.ppsItemDataService.getSelection();
	}

	public override performAction(filtered: IPPSItemEntity[]): void {
		filtered.forEach(item => {
			item.IsLive = false;
			this.ppsItemDataService.setModified(item);
		});
	}

	public override postProcess(): void {
		this.ppsItemDataService.refreshSelected().then(

		);
	}

}