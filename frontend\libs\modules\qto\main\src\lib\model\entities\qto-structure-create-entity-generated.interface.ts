/*
 * $Id$
 * Copyright(c) RIB Software GmbH
 */

export interface IQtoStructureCreateEntityGenerated {
	/*
	 * IsBillingBoq
	 */
	IsBillingBoq?: boolean | null;

	/*
	 * IsItem
	 */
	IsItem?: boolean | null;

	/*
	 * IsOverflow
	 */
	IsOverflow?: boolean | null;

	/*
	 * IsPesBoq
	 */
	IsPesBoq?: boolean | null;

	/*
	 * IsPrcBoq
	 */
	IsPrcBoq?: boolean | null;

	/*
	 * IsPrjBoq
	 */
	IsPrjBoq?: boolean | null;

	/*
	 * IsQtoBoq
	 */
	IsQtoBoq?: boolean | null;

	/*
	 * IsWipBoq
	 */
	IsWipBoq?: boolean | null;

	/*
	 * MainItemId
	 */
	MainItemId?: number | null;

	/*
	 * Number
	 */
	Number?: number | null;

	/*
	 * Numbers
	 */
	Numbers?: number[] | null;

	/*
	 * PageNumber
	 */
	PageNumber?: string | null;

	/*
	 * QtoType
	 */
	QtoType?: number | null;

	/*
	 * parentId
	 */
	parentId?: number | null;
}
