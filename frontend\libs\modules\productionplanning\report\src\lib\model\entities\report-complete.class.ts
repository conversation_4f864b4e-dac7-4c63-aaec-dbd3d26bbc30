/*
 * Copyright(c) RIB Software GmbH
 */

import { IReportEntity } from './report-entity.interface';
import { IReport2CostCodeEntity } from './report-2-cost-code-entity.interface';

import { ITimeSheetEntity } from './time-sheet-entity.interface';

import { CompleteIdentification } from '@libs/platform/common';
import { IPpsProductEntityGenerated, PpsProductCompleteEntity } from '@libs/productionplanning/shared';

export class ReportComplete implements CompleteIdentification<IReportEntity> {
	/**
	 * MainItemId
	 */
	public MainItemId: number = 0;

	/**
	 * PpsDocumentToDelete
	 */
	//public PpsDocumentToDelete?: IIIdentifyable[] | null = [];

	/**
	 * PpsDocumentToSave
	 */
	//public PpsDocumentToSave?: IIIdentifyable[] | null = [];

	/**
	 * Report
	 */
	public Report: IReportEntity[] | null = [];

	/**
	 * Report2CostCodeToDelete
	 */
	public Report2CostCodeToDelete?: IReport2CostCodeEntity[] | null = [];

	/**
	 * Report2CostCodeToSave
	 */
	public Report2CostCodeToSave?: IReport2CostCodeEntity[] | null = [];

	/**
	 * Report2ProductToDelete
	 */
	public Report2ProductToDelete?: IPpsProductEntityGenerated[] | null = [];

	/**
	 * Report2ProductToSave
	 */
	public Report2ProductToSave?: PpsProductCompleteEntity[] | null = [];

	/**
	 * Reports
	 */
	public Reports?: IReportEntity[] | null = [];

	/**
	 * TimeSheetToDelete
	 */
	public TimeSheetToDelete?: ITimeSheetEntity[] | null = [];

	/**
	 * TimeSheetToSave
	 */
	public TimeSheetToSave?: ITimeSheetEntity[] | null = [];
}
