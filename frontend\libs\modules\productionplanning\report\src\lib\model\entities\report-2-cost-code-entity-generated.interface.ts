/*
 * Copyright(c) RIB Software GmbH
 */

import { IEntityBase } from '@libs/platform/common';

export interface IReport2CostCodeEntityGenerated extends IEntityBase {
	/**
	 * CommentText
	 */
	CommentText?: string | null;

	/**
	 * CostCodeFk
	 */
	CostCodeFk: number;

	/**
	 * Id
	 */
	Id: number;

	/**
	 * Quantity
	 */
	Quantity: number;

	/**
	 * ReportFk
	 */
	ReportFk: number;

	/**
	 * UomFk
	 */
	UomFk: number;
}
