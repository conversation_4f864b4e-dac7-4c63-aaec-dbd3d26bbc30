
import * as _ from 'lodash';

import { EntityInfo } from '@libs/ui/business-base';
import {
	IPpsProductEntityGenerated,
	PpsProductSharedLayout,
	} from '@libs/productionplanning/shared';
import { PpsItemProductBehavior } from '../behaviors/pps-item-product-behavior.service';
import { PpsItemProductDataService } from '../services/product/pps-item-product-data.service';


export const PPS_ITEM_PRODUCT_ENTITY_INFO: EntityInfo = EntityInfo.create<IPpsProductEntityGenerated>({
	grid: {
		containerUuid: '92e45c26b45f4637980c0ba38bf8cd31',
		title: { key: 'productionplanning.common.product.itemProductTitle' },
		behavior: ctx => ctx.injector.get(PpsItemProductBehavior),
	},
	//validationService: ctx => {	},
	dataService: ctx => ctx.injector.get(PpsItemProductDataService),
	dtoSchemeId: { moduleSubModule: 'ProductionPlanning.Common', typeName: 'ProductDto' },
	permissionUuid: '92e45c26b45f4637980c0ba38bf8cd31',
	layoutConfiguration: _.merge(PpsProductSharedLayout, {
		overloads: {
		}
	})
});