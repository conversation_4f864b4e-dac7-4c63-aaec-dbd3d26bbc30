/*
 * Copyright(c) RIB Software GmbH
 */

import { Injectable, InjectionToken } from '@angular/core';
import { IEntityContainerBehavior, IGridContainerLink } from '@libs/ui/business-base';
import { IPpsMaterialCompEntity } from '../model/models';

export const PPS_MATERIAL_COMPATIBILITY_BEHAVIOR_TOKEN = new InjectionToken<PpsMaterialCompatibilityBehavior>('ppsMaterialCompatibilityBehavior');

@Injectable({
	providedIn: 'root'
})
export class PpsMaterialCompatibilityBehavior implements IEntityContainerBehavior<IGridContainerLink<IPpsMaterialCompEntity>, IPpsMaterialCompEntity> {

}