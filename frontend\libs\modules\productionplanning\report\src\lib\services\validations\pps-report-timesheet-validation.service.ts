/*
 * Copyright(c) RIB Software GmbH
 */
import { BaseValidationService, IEntityRuntimeDataRegistry, IValidationFunctions, ValidationInfo, ValidationResult } from '@libs/platform/data-access';
import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BasicsSharedDataValidationService } from '@libs/basics/shared';
import { ITimeSheetEntity } from '../../model/models';
import { ProductionplanningReportTimeSheetDataService } from '../productionplanning-report-time-sheet-data.service';

@Injectable({
	providedIn: 'root',
})
export class PpsReportTimeSheetValidationService extends BaseValidationService<ITimeSheetEntity> {
	private readonly dataService = inject(ProductionplanningReportTimeSheetDataService);
	protected readonly http = inject(HttpClient);
	private readonly validationUtils = inject(BasicsSharedDataValidationService);

	protected generateValidationFunctions(): IValidationFunctions<ITimeSheetEntity> {
		return {
			ResourceFk: this.validateIsMandatory,
			StartTime: this.validateStartTime,
			FinishTime: this.validateFinishTime,
			Date: this.validateIsMandatory,
		};
	}
	protected getEntityRuntimeData(): IEntityRuntimeDataRegistry<ITimeSheetEntity> {
		return this.dataService;
	}

	private validateStartTime(info: ValidationInfo<ITimeSheetEntity>): ValidationResult {
		return this.validationUtils.validatePeriod(this.getEntityRuntimeData(), info, <string>info.value, info.entity.StartTime ? info.entity.StartTime.toString() : '', 'EndTime');
	}
	private validateFinishTime(info: ValidationInfo<ITimeSheetEntity>): ValidationResult {
		return this.validationUtils.validatePeriod(this.getEntityRuntimeData(), info, <string>info.value, info.entity.EndTime ? info.entity.EndTime.toString() : '', 'StartTime');
	}
}
