import { inject } from '@angular/core';
import { IEntityProcessor, IEntityRuntimeDataRegistry } from '@libs/platform/data-access';
import { PpsSharedProductStatusDataService } from './pps-shared-product-status-data.service';
import { IPpsProductEntityGenerated } from '../../model/product/product-entity-generated.interface';
import _ from 'lodash';
import { PropertyPath } from '@libs/platform/common';


// export const PPS_SHARED_PRODUCT_PROCESSOR_TOKEN = new LazyInjectionToken<PpsSharedProductProcessorService<IPpsProductEntityGenerated>>('ppsSharedProductProcessor');
//
//
// @LazyInjectable({
// 	token: PPS_SHARED_PRODUCT_PROCESSOR_TOKEN,
// 	useAngularInjection: true
// })
export class PpsSharedProductProcessorService<T extends IPpsProductEntityGenerated> implements IEntityProcessor<T> {

	protected statusService = inject(PpsSharedProductStatusDataService);

	///protected dataService: IEntityRuntimeDataRegistry<T>
	public constructor(protected dataService: IEntityRuntimeDataRegistry<T>) {
	}

	public process(item: T): void {
		this.processItemSvg(item);
		this.processProdPlaceFk(item);
		this.processReadonlyOfFabricationUnitDateSlotColumnsThatValueIsEmpty(item);
		this.processReadonlyInPUModule(item);
		this.processAnnotationStatus(item);
	}

	public revertProcess(item: T): void {
	}


	public processReadonlyOfFabricationUnitDateSlotColumnsThatValueIsEmpty(item: T) {

	}

	public processProdPlaceFk(item: T) {
		const isProductionPlaceReadonly = item.IsGettingProdPlaceByNesting || !item.HasProcessConfiguredForProdPlaceAssignment;
		this.setColumnReadOnly(item, ['ProdPlaceFk'], isProductionPlaceReadonly);
	}

	public processItemSvg(item: T) {
		const statusList = this.statusService.getProductList();
		const status = _.find(statusList, { Id: item.ProductStatusFk });
		if (status && status.BackgroundColor) {
			item.BackgroundColor = status.BackgroundColor;
		}
	}

	public processReadonlyInPUModule(item: T) {

	}

	public trsReqColumnReadOnly(item: T) {

	}

	public setColumnReadOnly(item: T, propertyNames: PropertyPath<T>[], readonly: boolean) {
		// this.dataService.setEntityReadOnlyFields(item,propertyNames.map(e => {
		// 	return {
		// 		field: e,
		// 		readOnly: readonly
		// 	};
		// }));
	}

	public processAnnotationStatus(item: T) {

	}

}