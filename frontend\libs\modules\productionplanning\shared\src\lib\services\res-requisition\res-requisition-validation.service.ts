/*
 * Copyright(c) RIB Software GmbH
 */
import { inject, ProviderToken } from '@angular/core';
import { BaseValidationService, IEntityRuntimeDataRegistry, IValidationFunctions, PlatformSchemaService, ValidationServiceFactory } from '@libs/platform/data-access';
import { IRequisitionEntity } from '@libs/resource/interfaces';

export class PpsSharedResRequisitionValidationService extends BaseValidationService<IRequisitionEntity> {

	private validators: IValidationFunctions<IRequisitionEntity> | null = null;

	public constructor(private dataService: IEntityRuntimeDataRegistry<IRequisitionEntity>) {
		super();
		const schemaSvcToken: ProviderToken<PlatformSchemaService<IRequisitionEntity>> = PlatformSchemaService<IRequisitionEntity>;
		const platformSchemaService = inject(schemaSvcToken);

		// eslint-disable-next-line @typescript-eslint/no-this-alias
		const self = this;

		platformSchemaService.getSchema({ moduleSubModule: 'Resource.Requisition', typeName: 'RequisitionDto' })
			.then((scheme) => {
				self.validators = new ValidationServiceFactory<IRequisitionEntity>().provideValidationFunctionsFromScheme(scheme, self);
			});
	}


	protected generateValidationFunctions(): IValidationFunctions<IRequisitionEntity> {
		if (this.validators !== null) {
			return this.validators;
		}

		return {};
	}

	protected getEntityRuntimeData(): IEntityRuntimeDataRegistry<IRequisitionEntity> {
		return this.dataService;
	}


	// todo: extendDateshiftActivityValidation
	// let dataserviceModuleName = dataService.getModule().name;
	// if (_.includes(['productionplanning.mounting', 'transportplanning.requisition'], dataserviceModuleName)) {
	// 	activityDateshiftService.extendDateshiftActivityValidation(srv, dataService, 'resource.requisition', dataService.dateshiftId);
	// }

}
