{"name": "modules-qto-interfaces", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/modules/qto/interfaces/src", "projectType": "library", "targets": {"lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/modules/qto/interfaces/jest.config.ts"}}}, "tags": []}