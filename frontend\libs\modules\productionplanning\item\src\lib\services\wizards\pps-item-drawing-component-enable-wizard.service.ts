import { Injectable } from '@angular/core';
import { BasicsSharedSimpleActionWizardService, ISimpleActionOptions } from '@libs/basics/shared';

import { IEngDrawingComponentEntityGenerated } from '@libs/productionplanning/shared';
import {
	PpsItemProductTemplateComponentDataService
} from '../product-template/pps-item-product-template-component-data.service';


@Injectable({
	providedIn: 'root'
})
export class PpsItemDrawingComponentEnableWizardService extends BasicsSharedSimpleActionWizardService<IEngDrawingComponentEntityGenerated> {

	private readonly dataService = PpsItemProductTemplateComponentDataService.getInstance('a6293dfb6d944ae3a2c5a7ff3c55ed07');

	public onStartEnableWizard(): void {
		const option: ISimpleActionOptions<IEngDrawingComponentEntityGenerated> = {
			headerText: 'cloud.common.enableRecord',
			codeField: 'Description',
			doneMsg: 'productionplanning.item.wizard.drawingComponent.enableComponentDone',
			nothingToDoMsg: 'productionplanning.item.wizard.drawingComponent.componentAlreadyEnabled',
			questionMsg: 'cloud.common.questionEnableSelection'
		};
		this.startSimpleActionWizard(option);
	}

	public override filterToActionNeeded(selected: IEngDrawingComponentEntityGenerated[]): IEngDrawingComponentEntityGenerated[] {
		const filteredSelection: IEngDrawingComponentEntityGenerated[] = [];
		selected.forEach(item => {
			if (!item.IsLive) {
				filteredSelection.push(item);
			}
		});
		return filteredSelection;
	}

	public override getSelection(): IEngDrawingComponentEntityGenerated[] {
		return this.dataService?.getSelection() || [];
	}

	public override performAction(filtered: IEngDrawingComponentEntityGenerated[]): void {
		filtered.forEach(item => {
			item.IsLive = true;
			this.dataService?.setModified(item);
		});
	}

	public override postProcess(): void {
	}

}