<div>
	<!--	<main class="modal-body flex-box flex-column">-->
	<div class="input-group form-control">
		<input #searchInput type="text" class="form-control" (keydown.enter)="search(searchInput.value,$event)" autofocus>
		<span class="input-group-btn">
                <button class="btn btn-default input-sm btn-search tlb-icons icon-search" (click)="search(searchInput.value,$event)"></button>
            </span>
	</div>
	<div class="lookup-grid-container">
		<!--        <div class="subview-header toolbar" data-platform-collapsable-list data-ng-model="tools">-->
		<!--            <h2 class="title fix">{{'cloud.common.searchResults'| platformTranslate}} </h2>-->
		<!--            <div data-platform-menu-list data-list="tools" data-platform-refresh-on="[tools.version, tools.refreshVersion]"></div>-->
		<!--        </div>-->
		<div class="flex-box flex-column flex-element subview-content">
			<ui-common-grid #gridHost
			                [configuration]="configuration">
			</ui-common-grid>
		</div>
	</div>
	<!--	</main>-->
</div>
<ui-common-loading [loading]="loading"></ui-common-loading>