/*
 * Copyright(c) RIB Software GmbH
 */

import { EntityInfo } from '@libs/ui/business-base';
import { ProductionplanningReportProductDataService } from '../services/productionplanning-report-product-data.service';
import { IPpsProductEntityGenerated, PpsProductSharedLayout } from '@libs/productionplanning/shared';

export const PRODUCTIONPLANNING_REPORT_PRODUCT_ENTITY_INFO: EntityInfo = EntityInfo.create<IPpsProductEntityGenerated>({
	grid: {
		title: { key: 'productionplanning.report' + '.report2product.listTitle' },
	},

	dataService: (ctx) => ctx.injector.get(ProductionplanningReportProductDataService),

	dtoSchemeId: { moduleSubModule: 'ProductionPlanning.Common', typeName: 'ProductDto' },
	permissionUuid: 'ecffa60778344f779c17421b5ce06443',
	layoutConfiguration: PpsProductSharedLayout
});
