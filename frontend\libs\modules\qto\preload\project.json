{"name": "modules-qto-preload", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/modules/qto/preload/src", "prefix": "qto-preload", "tags": [], "projectType": "library", "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/modules/qto/preload/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}}}