/*
 * Copyright(c) RIB Software GmbH
 */

import { EntityInfo } from '@libs/ui/business-base';
import { IMdcProductDescriptionEntity } from './models';
import { createLookup, FieldType, ILayoutConfiguration, UiCommonLookupDataFactoryService } from '@libs/ui/common';
import { prefixAllTranslationKeys } from '@libs/platform/common';
import { PpsMaterialProductDescriptionDataService } from '../services/product-description/pps-material-product-description-data.service';
import { PpsSharedDrawingDialogLookupService } from '@libs/productionplanning/shared';
import { IPpsFormulaInstanceEntity, PPS_PROCESS_TEMPLATE_LOOKUP_PROVIDER_TOKEN } from '@libs/productionplanning/interfaces';
import { BasicsSharedLookupOverloadProvider } from '@libs/basics/shared';

export const PPS_MATERIAL_PRODUCT_DESC_ENTITY_INFO: EntityInfo = EntityInfo.create<IMdcProductDescriptionEntity>({
	grid: {
		title: { key: 'productionplanning.ppsmaterial.productDescription.listViewTitle' },
		containerUuid: '4f92e30bd24849b4bf2f6db12e83afde',
	},
	form: {
		title: { key: 'productionplanning.ppsmaterial.productDescription.detailViewTitle' },
		containerUuid: '211110394b224dd392b69c5b60fe4e80',
	},
	dataService: (ctx) => ctx.injector.get(PpsMaterialProductDescriptionDataService),
	dtoSchemeId: { moduleSubModule: 'ProductionPlanning.PpsMaterial', typeName: 'MdcProductDescriptionDto' },
	permissionUuid: '211110394b224dd392b69c5b60fe4e80',
	layoutConfiguration: async (context) => {
		return <ILayoutConfiguration<IMdcProductDescriptionEntity>>{
			groups: [
				{
					gid: 'basicData',
					title: {
						key: 'cloud.common.listProductDescTitle',
						text: 'Basic Data',
					},
					attributes: ['Code', 'DescriptionInfo', 'IsDefault', 'IsLive', 'UomFk', 'EngDrawingFk', 'ProcessTemplateFk', 'PpsFormulaInstanceFk'],
				},
				{
					gid: 'dimensions',
					title: {
						key: 'productionplanning.ppsmaterial.productDescription.dimensions',
						text: 'Dimensions',
					},
					attributes: ['Length', 'BasUomLengthFk', 'Width', 'BasUomWidthFk', 'Height', 'BasUomHeightFk', 'Area', 'Area2', 'Area3', 'BasUomAreaFk', 'Volume', 'Volume2', 'Volume3', 'BasUomVolumeFk'],
				},
				{
					gid: 'propertiesGroup',
					title: {
						key: 'productionplanning.ppsmaterial.productDescription.propertiesGroup',
						text: 'Documentation',
					},
					attributes: ['Weight', 'Weight2', 'Weight3', 'BasUomWeightFk', 'IsolationVolume', 'ConcreteVolume', 'ConcreteQuality'],
				},
				{
					gid: 'userDefTextGroup',
					title: {
						key: 'cloud.common.UserdefTexts',
						text: 'Userdefined Texts',
					},
					attributes: ['Userdefined1', 'Userdefined2', 'Userdefined3', 'Userdefined4', 'Userdefined5'],
				},
			],
			overloads: {
				EngDrawingFk: {
					type: FieldType.Lookup,
					lookupOptions: createLookup({
						dataServiceToken: PpsSharedDrawingDialogLookupService,
						showClearButton: false,
					}),
				},
				ProcessTemplateFk: (await context.lazyInjector.inject(PPS_PROCESS_TEMPLATE_LOOKUP_PROVIDER_TOKEN)).provideDialogLookupOverload(),
				PpsFormulaInstanceFk: {
					type: FieldType.Lookup,
					lookupOptions: createLookup<IMdcProductDescriptionEntity, IPpsFormulaInstanceEntity>({
						showClearButton: true,
						dataService: context.injector.get(UiCommonLookupDataFactoryService).fromLookupType('PpsFormulaInstance', {
							uuid: '0e8e82f08f69411cb26c4379d6405c03',
							valueMember: 'Id',
							displayMember: 'DescriptionInfo.Translated',
							gridConfig: {
								columns: [
									{
										id: 'Code',
										model: 'Code',
										type: FieldType.Code,
										label: { text: 'Code', key: 'cloud.common.entityCode' },
										sortable: true,
										visible: true,
										readonly: true,
									},
									{
										id: 'Description',
										model: 'DescriptionInfo.Translated',
										type: FieldType.Description,
										label: { text: 'Description', key: 'cloud.common.entityDescription' },
										sortable: true,
										visible: true,
										readonly: true,
									},
								],
							},
						}),
					}),
				},
				//TODO: should be filters by dimensions for uoms
				UomFk: BasicsSharedLookupOverloadProvider.provideUoMLookupOverload(false),
				BasUomWeightFk: BasicsSharedLookupOverloadProvider.provideUoMLookupOverload(false),
				BasUomAreaFk: BasicsSharedLookupOverloadProvider.provideUoMLookupOverload(false),
				BasUomVolumeFk: BasicsSharedLookupOverloadProvider.provideUoMLookupOverload(false),
				BasUomHeightFk: BasicsSharedLookupOverloadProvider.provideUoMLookupOverload(false),
				BasUomLengthFk: BasicsSharedLookupOverloadProvider.provideUoMLookupOverload(false),
				BasUomWidthFk: BasicsSharedLookupOverloadProvider.provideUoMLookupOverload(false),
			},
			labels: {
				...prefixAllTranslationKeys('cloud.common.', {
					Code: { key: 'entityCode', text: 'Code' },
					DescriptionInfo: { key: 'entityDescription', text: 'Description' },
					IsDefault: { key: 'entityIsDefault', text: 'Is Default' },
					IsLive: { key: 'entityIsLive', text: 'IsLive' },
					UomFk: { key: 'entityUoM', text: 'UoM' },
					Userdefined1: {
						key: 'entityUserDefText',
						params: { p_0: '1' },
					},
					Userdefined2: {
						key: 'entityUserDefText',
						params: { p_0: '2' },
					},
					Userdefined3: {
						key: 'entityUserDefText',
						params: { p_0: '3' },
					},
					Userdefined4: {
						key: 'entityUserDefText',
						params: { p_0: '4' },
					},
					Userdefined5: {
						key: 'entityUserDefText',
						params: { p_0: '5' },
					},
				}),
				...prefixAllTranslationKeys('productionplanning.ppsmaterial.', {
					EngDrawingFk: { key: 'productDescription.engDrawing', text: '*Drawing' },
					ProcessTemplateFk: { key: 'productDescription.processTemplateFk', text: '*Process Template' },
					PpsFormulaInstanceFk: { key: 'productDescription.ppsFormulaInstanceFk', text: '*Formula Instance' },
					Width: { key: 'productDescription.width', text: 'Width' },
					Length: { key: 'productDescription.length', text: 'Length' },
					Height: { key: 'productDescription.height', text: 'Height' },
					Area: { key: 'productDescription.area', text: 'Area' },
					IsolationVolume: { key: 'productDescription.isolationVolume', text: '*Isolation Volume' },
					ConcreteVolume: { key: 'productDescription.concreteVolume', text: '*Concrete Volume' },
					ConcreteQuality: { key: 'productDescription.concreteQuality', text: '*Concrete Quality' },
					Weight: { key: 'productDescription.weight', text: 'Weight' },
				}),
				...prefixAllTranslationKeys('productionplanning.common.', {
					BasUomLengthFk: { key: 'product.lengthUoM', text: 'Length UoM' },
					BasUomWidthFk: { key: 'product.widthUoM', text: 'Width UoM' },
					BasUomHeightFk: { key: 'product.heightUoM', text: 'Height UoM' },
					Area2: { key: 'product.area2', text: 'Area2' },
					Area3: { key: 'product.area3', text: 'Area3' },
					BasUomAreaFk: { key: 'product.areaUoM', text: 'Area UoM' },
					Volume: { key: 'product.volume', text: '*Volume' },
					Volume2: { key: 'product.volume2', text: '*Volume2' },
					Volume3: { key: 'product.volume3', text: '*Volume3' },
					BasUomVolumeFk: { key: 'product.volumeUoM', text: 'Volume UoM' },
					Weight2: { key: 'product.weight2', text: 'Weight2' },
					Weight3: { key: 'product.weight3', text: 'Weight3' },
					BasUomWeightFk: { key: 'product.weightUoM', text: 'Weight UoM' },
				}),
			},
		};
	},
});
