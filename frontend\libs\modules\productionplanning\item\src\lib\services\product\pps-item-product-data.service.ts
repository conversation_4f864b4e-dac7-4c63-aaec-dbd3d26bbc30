
import _, { get } from 'lodash';
import { inject, Injectable } from '@angular/core';

import { PlatformConfigurationService, PlatformHttpService } from '@libs/platform/common';

import {
	DataServiceFlatNode, IDataServiceEndPointOptions,
	IDataServiceOptions,
	IDataServiceRoleOptions, IEntityProcessor,
	ServiceRole
} from '@libs/platform/data-access';
import { PPSItemComplete } from '../../model/models';
import { PpsItemDataService } from '../pps-item-data.service';
import {
	IPPSEventEntity,
	IPpsProductEntityGenerated,
	PpsProductCompleteEntity, PpsSharedProductProcessorService,
	PpsSharedProductStatusDataService
} from '@libs/productionplanning/shared';
import { IEventTypeEntity } from '@libs/productionplanning/configuration';
import { IPPSItemEntity } from '@libs/productionplanning/common';


@Injectable({
	providedIn: 'root',
})
export class PpsItemProductDataService extends DataServiceFlatNode<IPpsProductEntityGenerated, PpsProductCompleteEntity, IPPSItemEntity, PPSItemComplete> {

	private https = inject(PlatformHttpService);
	private configurationService = inject(PlatformConfigurationService);
	private statusService = inject(PpsSharedProductStatusDataService);
	private readonly processorService: IEntityProcessor<IPpsProductEntityGenerated>;
	//private dialogService: PpsItemProductManualCreationDialogService;

	public constructor(private ppsItemDataService: PpsItemDataService) {
		const options: IDataServiceOptions<IPpsProductEntityGenerated> = {
			apiUrl: 'productionplanning/common/product',
			readInfo: <IDataServiceEndPointOptions>{
				endPoint: 'customListForItem',
				usePost: false,
			},
			createInfo: {
				endPoint: 'createmanually'
			},
			roleInfo: <IDataServiceRoleOptions<IPpsProductEntityGenerated>>{
				role: ServiceRole.Node,
				itemName: 'Product',
				parent: ppsItemDataService,
			},
			entityActions: { createSupported: false, deleteSupported: true },
		};
		super(options);

		this.processorService = new PpsSharedProductProcessorService<IPpsProductEntityGenerated>(this);
		this.processor.addProcessor([this.processorService]);

		//this.dialogService = inject(PpsItemProductManualCreationDialogService);
	}

	public override canDelete(): boolean {
		const product = this.getSelectedEntity();
		if (product !== null) {
			if (product.PpsItemStockFk !== null && product.PpsItemStockFk !== product.ItemFk) {
				return false;
			}

			if (this.ppsItemDataService.getSelectedEntity()?.IsForPreliminary === true) {
				return false;
			}

			if (product.ProductStatusFk) {
				return this.statusService.allowProductToBeDeleted(this.getSelection());
			}
		}
		return false;
	}

	protected override provideLoadPayload() {
		const parentSelection = this.getSelectedParent();
		if (!parentSelection) {
			return {};
		}
		return {
			itemFk: get(parentSelection, 'Id'),
			moveToRoot: 'true',
		};
	}

	public override registerByMethod(): boolean {
		return true;
	}

	public override isParentFn(parentKey: IPPSItemEntity, entity: IPpsProductEntityGenerated): boolean {
		return entity.ItemFk === parentKey.Id;
	}

	protected override onLoadSucceeded(loaded: object): IPpsProductEntityGenerated[] {
		if (loaded) {
			return get(loaded, 'Main', []);
		}
		return [];
	}

	public override registerNodeModificationsToParentUpdate(complete: PPSItemComplete, modified: PpsProductCompleteEntity[], deleted: IPpsProductEntityGenerated[]) {
		if (modified && modified.length > 0) {
			complete.ProductToSave = modified;
		}

		if (deleted && deleted.length > 0) {
			complete.ProductToDelete = deleted;
		}
	}

	public override createUpdateEntity(modified: IPpsProductEntityGenerated | null): PpsProductCompleteEntity {
		const complete = new PpsProductCompleteEntity();
		if (modified !== null) {
			complete.MainItemId = modified.Id;
			complete.Products = [modified];
		}

		return complete;
	}

	public override getSavedEntitiesFromUpdate(complete: PPSItemComplete): IPpsProductEntityGenerated[] {
		return (complete && complete.ProductToSave)
			? complete.ProductToSave.map(e => e.Product!)
			: [];
	}

	public override getModificationsFromUpdate(complete: PpsProductCompleteEntity): IPpsProductEntityGenerated[] {
		if (complete.Products === null) {
			complete.Products = [];
		}
		return complete.Products;
	}

	public getParentItem(): IPPSItemEntity | undefined{
		return this.getSelectedParent();
	}

	public moveToRoot() {
	}

	public canMoveToRoot() {
		const selectedItem = this.getSelectedParent();
		if (selectedItem && selectedItem.IsForPreliminary) {
			return false;
		}
		// check if has selected products
		const selectedEntities = this.getSelection();
		if(_.isNil(selectedEntities) || selectedEntities.length <= 0){
			return false;
		}
		return selectedEntities.every(function (prod) {
			return !!prod.CanAssign;
		});
	}

	public getPpsItemProductionSetEvent(): IPPSEventEntity | undefined{
		const ppsItem = this.getSelectedParent();
		const events = get(ppsItem, 'EventEntities');
		const eventTypeLookup: IEventTypeEntity[] = [];//basicsLookupdataLookupDescriptorService.getData('EventType'.toLowerCase());
		if(events !== undefined){
			return _.find(events, (event: IPPSEventEntity) =>{
				return eventTypeLookup[event.EventTypeFk]?.PpsEntityFk === 15;
			});
		}
		return undefined;
	}

	public canCreateMannual() {
		const selectedParent = this.getSelectedParent();
		return selectedParent && selectedParent.ProductDescriptionFk && !selectedParent.IsForPreliminary;
		//isLoadingForSingleOrMultipleManualProductCreation ||
	}

	public validationDatashift(entity: IPpsProductEntityGenerated, endDate: Date, bFirst: boolean) {

	}

	public backToStock() {
	}

	public takeFromStock() {
	}

	public serialProduction() {
	}

	public createLinkageToModel() {
	}
}