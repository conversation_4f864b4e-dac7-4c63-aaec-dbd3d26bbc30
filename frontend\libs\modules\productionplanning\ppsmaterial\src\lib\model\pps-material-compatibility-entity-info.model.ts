/*
 * Copyright(c) RIB Software GmbH
 */

import { EntityInfo } from '@libs/ui/business-base';
import { PpsMaterialCompatibilityBehavior } from '../behaviors/pps-material-compatibility-behavior.service';
import { PpsMaterialCompatibilityDataService } from '../services/compatibility/pps-material-compatibility-data.service';
import { PpsMaterialCompatibilityValidationService } from '../services/compatibility/pps-material-compatibility-validation.service';
import { IPpsMaterialCompEntity } from './models';
import { PPS_MATERIAL_COMPATIBILITY_LAYOUT } from './pps-material-compatibility-layout.model';

export const PPS_MATERIAL_COMPATIBILITY_ENTITY_INFO: EntityInfo = EntityInfo.create<IPpsMaterialCompEntity>({
	grid: {
		title: { key: 'productionplanning.ppsmaterial.ppsMaterialComp.listViewTitle' },
		behavior: ctx => ctx.injector.get(PpsMaterialCompatibilityBehavior),
		containerUuid: 'addd32320fb24fd3b047db0ab575816c'
	},
	dataService: ctx => ctx.injector.get(PpsMaterialCompatibilityDataService),
	validationService: ctx => ctx.injector.get(PpsMaterialCompatibilityValidationService),
	dtoSchemeId: { moduleSubModule: 'ProductionPlanning.PpsMaterial', typeName: 'PpsMaterialCompDto' },
	permissionUuid: 'addd32320fb24fd3b047db0ab575816c',
	layoutConfiguration: PPS_MATERIAL_COMPATIBILITY_LAYOUT,

});