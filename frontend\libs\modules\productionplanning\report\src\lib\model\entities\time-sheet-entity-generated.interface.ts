/*
 * Copyright(c) RIB Software GmbH
 */

import { IEntityBase } from '@libs/platform/common';

export interface ITimeSheetEntityGenerated extends IEntityBase {
	/**
	 * BreakTime
	 */
	BreakTime?: string | null;

	/**
	 * CommentText
	 */
	CommentText?: string | null;

	/**
	 * CountryFk
	 */
	CountryFk?: number | null;

	/**
	 * Date
	 */
	Date: Date | string;

	/**
	 * Description
	 */
	Description?: string | null;

	/**
	 * Doctor
	 */
	Doctor: boolean;

	/**
	 * Driver
	 */
	Driver: boolean;

	/**
	 * EndTime
	 */
	EndTime: string;

	/**
	 * HadBreak
	 */
	HadBreak: boolean;

	/**
	 * Id
	 */
	Id: number;

	/**
	 * Leader
	 */
	Leader: boolean;

	/**
	 * OverNight
	 */
	OverNight: boolean;

	/**
	 * ReportFk
	 */
	ReportFk: number;

	/**
	 * ResourceFk
	 */
	ResourceFk: number;

	/**
	 * Sick
	 */
	Sick: boolean;

	/**
	 * StartTime
	 */
	StartTime: string;

	/**
	 * TimeOff
	 */
	TimeOff: boolean;

	/**
	 * Vacation
	 */
	Vacation: boolean;
}
