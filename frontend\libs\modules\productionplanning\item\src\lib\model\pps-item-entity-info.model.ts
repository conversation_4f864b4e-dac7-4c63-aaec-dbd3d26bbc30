/*
 * $Id$
 * Copyright(c) RIB Software GmbH
 */
import {IGridTreeConfiguration} from '@libs/ui/common';
import {EntityInfo} from '@libs/ui/business-base';
import {PpsItemDataService} from '../services/pps-item-data.service';
import {PPS_ITEM_BEHAVIOR_TOKEN} from '../behaviors/pps-item-behavior.service';
import {PPS_ITEM_STRUCTURE_BEHAVIOR_TOKEN} from '../behaviors/pps-item-structure-behavior.service';
import {
    IPPSItemEntity,
    PpsCommonItemLayoutService
} from '@libs/productionplanning/common';
export const PPS_ITEM_ENTITY_INFO: EntityInfo = EntityInfo.create<IPPSItemEntity>({
    grid: {
        containerUuid: '3598514b62bc409ab6d05626f7ce304b',
        title: {key: 'productionplanning.item.listTitle'},
        behavior: PPS_ITEM_BEHAVIOR_TOKEN
    },
    tree: {
        containerUuid: '5907fffe0f9b44588254c79a70ba3af1',
        title: 'productionplanning.item.treeTitle',
        behavior: PPS_ITEM_STRUCTURE_BEHAVIOR_TOKEN,
        treeConfiguration: ctx => {
            const service = ctx.injector.get(PpsItemDataService);
            return {
                parent: function (entity: IPPSItemEntity) {
                    return service.parentOf(entity);
                },
                children: function (entity: IPPSItemEntity) {
                    return service.childrenOf(entity);
                }
            } as IGridTreeConfiguration<IPPSItemEntity>;
        },
    },
    form: {
        title: {key: 'productionplanning.item.detailTitle'},
        containerUuid: '2ded3fea233f40f4a00a5d9636297df8',
    },
    dataService: ctx => ctx.injector.get(PpsItemDataService),
    dtoSchemeId: {moduleSubModule: 'ProductionPlanning.Item', typeName: 'PPSItemDto'},
    permissionUuid: '5907fffe0f9b44588254c79a70ba3af1',
    layoutConfiguration: (ctx) => ctx.injector.get(PpsCommonItemLayoutService).generateConfig(),
});