import { IRequisitionEntity/*, IReservationEntity*/ } from '@libs/resource/interfaces';

// maps to RIB.Visual.Basics.Core.Core.IPpsDerivedEventCompleteEntity on server side
export interface IPpsDerivedEventCompleteEntity {

	// public IEnumerable<IIdentifyable> ResRequisitionToSave { get; set; }

	ResRequisitionToSave?: IRequisitionEntity[];
	ResRequisitionToDelete?: IRequisitionEntity[];

	// todo: wait for implementation of resouce.reservation module
	// ResReservationToSave?: IReservationEntity[];
	// ResReservationToDelete?: IReservationEntity[];
}