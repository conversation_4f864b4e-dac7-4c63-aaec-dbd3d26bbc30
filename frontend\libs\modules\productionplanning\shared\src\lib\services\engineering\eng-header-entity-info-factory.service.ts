import {IEntityIdentification, IInitializationContext} from '@libs/platform/common';
import {IEntitySelection} from '@libs/platform/data-access';
import {EntityInfo} from '@libs/ui/business-base';
import {IEngHeaderEntity} from '../../model/engineering/eng-header-entity.interface';
import {EngHeaderDataService} from './eng-header-data.service';
import {EngHeaderLayoutService} from './eng-header-layout.service';
import {ILayoutConfiguration} from '@libs/ui/common';
import {runInInjectionContext} from '@angular/core';
// import  {EngHeaderDataService} from ''
export class EngHeaderEntityInfoFactoryService {
    public static create<PT extends IEntityIdentification> (options: {
        parentServiceFn: (context: IInitializationContext) => IEntitySelection<PT>,
        containerUuid: string,
        formContainerUuid: string,
        permissionUuid: string

    }) : EntityInfo {
        return EntityInfo.create<IEngHeaderEntity>({
            grid: {
                containerUuid: options.containerUuid,
                title: {text: 'productionplanning.engineering.headerListTitle'}
            },
            form: {
                containerUuid:options.formContainerUuid,
                title: {text: 'productionplanning.engineering.headerDetailTitle'},
            },
            permissionUuid: options.permissionUuid,
            dataService: ctx => runInInjectionContext(ctx.injector, () => new EngHeaderDataService(options.parentServiceFn(ctx))),
            dtoSchemeId: {moduleSubModule: 'ProductionPlanning.Engineering', typeName: 'EngHeaderDto'},
            layoutConfiguration: context =>  {
                return context.injector.get(EngHeaderLayoutService).generateLayout() as ILayoutConfiguration<IEngHeaderEntity> ;
            }
        });
    }
}