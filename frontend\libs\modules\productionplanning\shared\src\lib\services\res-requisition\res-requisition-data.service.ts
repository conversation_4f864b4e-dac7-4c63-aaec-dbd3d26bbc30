import {
	IEntityIdentification,
} from '@libs/platform/common';
import {
	DataServiceFlatLeaf,
	IDataServiceChildRoleOptions,
	IDataServiceOptions,
	IEntitySelection,
	ServiceRole
} from '@libs/platform/data-access';
import { get, isNull } from 'lodash';

import { IRequisitionEntity } from '@libs/resource/interfaces';
import { IPpsDerivedEventCompleteEntity } from '../../model/event/event-shared';

export class PpsSharedResRequisitionDataService<PT extends IEntityIdentification>
	extends DataServiceFlatLeaf<IRequisitionEntity, PT, IPpsDerivedEventCompleteEntity> {

	public constructor(parentService: IEntitySelection<PT>) {
		const options: IDataServiceOptions<IRequisitionEntity> = {
			apiUrl: 'resource/requisition',
			readInfo: {
				endPoint: 'listForMntActivity', // at the moment,in the server side,'listForMntActivity' also equals to 'list By PpsEvent'
				usePost: false
			},
			createInfo: {
				endPoint: 'create',
				usePost: true,
			},
			roleInfo: <IDataServiceChildRoleOptions<IRequisitionEntity, PT, IPpsDerivedEventCompleteEntity>>{
				role: ServiceRole.Leaf,
				itemName: 'ResRequisition',
				parent: parentService
			}
		};

		super(options);
	}

	protected override provideCreatePayload(): object {
		return {
			Id: this.getSelectedParent()?.Id
		};
	}

	protected override onCreateSucceeded(created: IRequisitionEntity): IRequisitionEntity {
		const parent = this.getSelectedParent();

		const ppsEventId = get(parent, 'PpsEventFk');
		created.PpsEventFk = ppsEventId as number;

		const prjId = get(parent, 'ProjectId') ?? get(parent, 'ProjectFk') ?? get(parent, 'PrjProjectFk') ?? -1;
		created.ProjectFk = prjId as number;

		const jobId = get(parent, 'LgmJobFk') ?? -1;
		created.JobFk = jobId as number;

		const requestedFrom = get(parent, 'PlannedStart');
		created.RequestedFrom = requestedFrom as string;
		const requestedTo = get(parent, 'PlannedFinish');
		created.RequestedTo = requestedTo as string;

		return created;
	}

	protected override provideLoadPayload(): object {
		return {
			PpsEventId: get(this.getSelectedParent(), 'PpsEventFk')
		};
	}

	protected override onLoadSucceeded(loaded: object): IRequisitionEntity[] {
		return loaded as IRequisitionEntity[];
	}

	public override isParentFn(parentKey: object, entity: IRequisitionEntity): boolean {
		return entity.PpsEventFk === get(parentKey, 'PpsEventFk');
	}

	public override registerByMethod(): boolean {
		return true;
	}

	public override registerModificationsToParentUpdate(parentUpdate: IPpsDerivedEventCompleteEntity, modified: IRequisitionEntity[], deleted: IRequisitionEntity[]): void {
		if (modified && modified.length > 0) {
			parentUpdate.ResRequisitionToSave = modified;
		}

		if (deleted && deleted.length > 0) {
			parentUpdate.ResRequisitionToDelete = deleted;
		}
	}

	public override getSavedEntitiesFromUpdate(parentUpdate: IPpsDerivedEventCompleteEntity): IRequisitionEntity[] {
		if (parentUpdate && !isNull(parentUpdate.ResRequisitionToSave)) {
			return parentUpdate.ResRequisitionToSave!;
		}
		return [];
	}
}