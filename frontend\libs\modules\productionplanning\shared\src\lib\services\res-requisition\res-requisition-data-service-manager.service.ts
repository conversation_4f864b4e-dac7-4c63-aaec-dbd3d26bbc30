import { runInInjectionContext } from '@angular/core';
import { IEntityIdentification, IInitializationContext } from '@libs/platform/common';
import { IEntityList, IEntityModification, IEntityRuntimeDataRegistry, IEntitySelection } from '@libs/platform/data-access';
import { IRequisitionEntity } from '@libs/resource/interfaces';
import { IPpsEntityInfoOptions } from '../../model';
import { PpsSharedResRequisitionDataService } from './res-requisition-data.service';

export class PpsSharedResRequisitionDataServiceManager {

	private static _dataServiceCache = new Map<string, IEntityModification<IRequisitionEntity> & IEntitySelection<IRequisitionEntity> & IEntityList<IRequisitionEntity> & IEntityRuntimeDataRegistry<IRequisitionEntity>>();

	public static getDataService<PT extends IEntityIdentification/*, PU extends object*/>(
		options: IPpsEntityInfoOptions<PT>,
		context: IInitializationContext,
	) {
		const key = options.containerUuid;

		let instance = PpsSharedResRequisitionDataServiceManager._dataServiceCache.get(key);
		if (!instance) {
			instance = runInInjectionContext(context.injector, () => new PpsSharedResRequisitionDataService(
				options.parentServiceFn(context)
			));
			PpsSharedResRequisitionDataServiceManager._dataServiceCache.set(key, instance);
		}
		return instance;
	}
}