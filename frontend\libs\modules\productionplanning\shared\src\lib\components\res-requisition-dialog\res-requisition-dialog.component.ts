/*
 * Copyright(c) RIB Software GmbH
 */
import { HttpClient } from '@angular/common/http';
import { Component, Inject, OnInit, ViewChild } from '@angular/core';
import { IEntityBase, PlatformConfigurationService } from '@libs/platform/common';
import { ProjectSharedLookupService } from '@libs/project/shared';
import { IRequisitionEntity } from '@libs/resource/interfaces';
import { createLookup, FieldType, GridComponent, IGridConfiguration } from '@libs/ui/common';
import { get } from 'lodash';
import { firstValueFrom } from 'rxjs';

/**
 * res-requisition-dialog component
 * This component is used to display a dialog for selecting resource requisitions.
 * It allows users to search and select requisitions based on a project filter.
 * The selected requisitions can be returned when the dialog is confirmed.
 */
@Component({
	selector: 'productionplanning-shared-res-requisition-dialog',
	templateUrl: './res-requisition-dialog.component.html',
	styleUrls: ['./res-requisition-dialog.component.scss'],
})
export class ResRequisitionDialogComponent implements OnInit {

	@ViewChild('gridHost')
	private gridHost: GridComponent<IRequisitionEntity> | undefined;

	public get selectedEntities() {
		return this.gridHost!.selection;
	}

	private cacheData: IRequisitionEntity[] = []; // current page data
	public loading: boolean = false;
	public configuration: IGridConfiguration<IRequisitionEntity> = {
		uuid: '',
		columns: [],
		items: [],
	};

	public constructor(
		@Inject('parentSelected') public parentSelected: IEntityBase,
		private readonly http: HttpClient,
		private readonly configService: PlatformConfigurationService,
	) { }

	public async refresh() {
		this.loading = true;
		if (this.parentSelected) {
			const prjId = get(this.parentSelected, 'ProjectFk') ?? get(this.parentSelected, 'ProjectId');
			this.cacheData = await firstValueFrom(
				this.http.post<IRequisitionEntity[]>(this.configService.webApiBaseUrl + 'resource/requisition/lookuplistbyfilter', {
					projectFk: prjId
				}),
			);
			this.search('', null);
		}
		this.loading = false;
	}

	public async ok() {
		return { selectedEntities: this.selectedEntities };
	}

	private initConfig() {
		this.configuration = {
			uuid: 'e75791176aec4352aa5ffbe71ed0f25b',
			//skipPermissionCheck: true,
			items: [],
			columns: [
				{
					id: 'code',
					model: 'Code',
					type: FieldType.Description,
					label: {
						text: 'Code',
						key: 'cloud.common.entityCode',
					},
					visible: true,
					sortable: true,
					readonly: true,
				},
				{
					id: 'desc',
					model: 'Description',
					type: FieldType.Description,
					label: {
						text: 'Description',
						key: 'cloud.common.entityDescription',
					},
					visible: true,
					sortable: true,
					readonly: true,
				},
				{
					id: 'requestedFrom',
					model: 'RequestedFrom',
					type: FieldType.DateTimeUtc,
					label: {
						text: 'RequestedFrom',
						key: 'resource.requisition.entityRequestedFrom',
					},
					visible: true,
					sortable: true,
					readonly: true,
				},
				{
					id: 'requestedTo',
					model: 'RequestedTo',
					type: FieldType.DateTimeUtc,
					label: {
						text: 'RequestedTo',
						key: 'resource.requisition.entityRequestedTo',
					},
					visible: true,
					sortable: true,
					readonly: true,
				},
				// todo TrsRequisitionFk
				{
					id: 'projectName',
					model: 'ProjectFk',
					type: FieldType.Lookup,
					lookupOptions: createLookup({
						dataServiceToken: ProjectSharedLookupService,
						displayMember: 'ProjectName',
					}),
					label: { key: 'cloud.common.entityProjectName' },
					sortable: false,
					readonly: true,
				},
			],
		};
	}

	public async ngOnInit() {
		this.loading = true;
		this.initConfig();
		await this.refresh();
		this.loading = false;
	}

	public search(filter: string, event: Event | null): void {
		this.configuration = {
			...this.configuration,
			items: filter.length > 0 ? [...this.cacheData.filter((e) => e.Code?.toLowerCase().includes(filter.toLowerCase()) || e.Description?.toLowerCase().includes(filter.toLowerCase()))] : [...this.cacheData],
		};
	}
}
