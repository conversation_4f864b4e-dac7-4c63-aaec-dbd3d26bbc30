// remark: current file is copied from basics-material-material-group-data.service in basics.material,
// should be replaced by other way(like <PERSON>zy<PERSON>n<PERSON>Token from basics.material module) in the future
/*
 * Copyright(c) RIB Software GmbH
 */

import { Injectable } from '@angular/core';
import { DataServiceFlatLeaf, IDataServiceChildRoleOptions, IDataServiceOptions, ServiceRole } from '@libs/platform/data-access';
import { PpsMaterialComplete } from '../../model/entities/pps-material-complete.class';
import { IMdcProductDescriptionEntity } from '../../model/entities/mdc-product-description-entity.interface';
import { IMdcProductDescParamEntity } from '../../model/entities/mdc-product-desc-param-entity.interface';
import { PpsMaterialProductDescriptionDataService } from './pps-material-product-description-data.service';

/**
 * Material group data service
 */
@Injectable({
	providedIn: 'root',
})
export class PpsMaterialProductDescParametersDataService extends DataServiceFlatLeaf<IMdcProductDescParamEntity, IMdcProductDescriptionEntity, PpsMaterialComplete> {
	/**
	 * The constructor
	 */
	public constructor(private readonly _parentService: PpsMaterialProductDescriptionDataService) {
		const options: IDataServiceOptions<IMdcProductDescParamEntity> = {
			apiUrl: 'productionplanning/ppsmaterial/mdcproductdescparam',
			roleInfo: <IDataServiceChildRoleOptions<IMdcProductDescParamEntity, IMdcProductDescriptionEntity, PpsMaterialComplete>>{
				role: ServiceRole.Leaf,
				itemName: 'MdcProductDescParam',
				parent: _parentService,
			},
			createInfo: {
				endPoint: 'create',
				usePost: true,
				prepareParam: (identity) => {
					return {
						Id: _parentService.getSelectedEntity()?.Id || -1,
					};
				},
			},
			readInfo: {
				endPoint: 'list',
				usePost: false,
				prepareParam: (ident) => {
					return { productDescriptionFk: ident.pKey1 || -1 };
				},
			},
		};
		super(options);
	}
}
