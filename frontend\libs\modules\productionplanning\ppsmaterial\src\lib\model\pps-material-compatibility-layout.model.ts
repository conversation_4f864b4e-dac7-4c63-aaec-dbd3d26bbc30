/*
 * Copyright(c) RIB Software GmbH
 */

import { prefixAllTranslationKeys } from '@libs/platform/common';
import { createLookup, FieldType, ILayoutConfiguration } from '@libs/ui/common';
import { MaterialNewLookupService } from '../services/material-new-lookup.service';
import { IPpsMaterialCompEntity } from './models';

export const PPS_MATERIAL_COMPATIBILITY_LAYOUT: ILayoutConfiguration<IPpsMaterialCompEntity> = {
	groups: [
		{
			gid: 'baseGroup',
			attributes: ['MdcMaterialItemFk', 'Remark', 'Userdefined1', 'Userdefined2', 'Userdefined3', 'Userdefined4', 'Userdefined5', 'Userflag1', 'Userflag2'],
		},
	],
	labels: {
		...prefixAllTranslationKeys('cloud.common.', {
			baseGroup: { key: 'entityProperties', text: '*Basic Data' },
			Remark: { key: 'entityRemark', text: '*Remark' },
			userDefTextGroup: { key: 'UserdefTexts' },
			Userdefined1: { key: 'entityUserDefined', params: { 'p_0': '1' } },
			Userdefined2: { key: 'entityUserDefined', params: { 'p_0': '2' } },
			Userdefined3: { key: 'entityUserDefined', params: { 'p_0': '3' } },
			Userdefined4: { key: 'entityUserDefined', params: { 'p_0': '4' } },
			Userdefined5: { key: 'entityUserDefined', params: { 'p_0': '5' } }
		}),
		...prefixAllTranslationKeys('productionplanning.ppsmaterial.', {
			basicConfiguration: { key: 'ppsEventTypeRelation.basicConfiguration', text: '*Basic Configuration' },
			MdcMaterialItemFk: { key: 'ppsMaterialComp.ppsMaterialItemFk', text: '*Material Planningunit' },
			UserFlag1: { key: 'ppsMaterialComp.userFlag1', text: '*User Flag 1' },
			UserFlag2: { key: 'ppsMaterialComp.userFlag2', text: '*User Flag 2' },
		}),

	},
	overloads: {
		MdcMaterialItemFk: {
			type: FieldType.Lookup,
			lookupOptions: createLookup({
				dataServiceToken: MaterialNewLookupService,
				descriptionMember: 'Code',
			}),
		},

	}

};
