/*
 * Copyright(c) RIB Software GmbH
 */
import {
	DataServiceFlatLeaf,
	IDataServiceChildRoleOptions,
	IDataServiceEndPointOptions,
	IDataServiceOptions,
	ServiceRole
} from '@libs/platform/data-access';

import { Injectable } from '@angular/core';
import { IMaterialNewEntity, IPpsEventTypeRelEntity, PpsMaterialComplete } from '../../model/models';
import { PpsMaterialRecordDataService } from '../material/material-record-data.service';
import { isNull } from 'lodash';

@Injectable({
	providedIn: 'root'
})
export class PpsEventTypeRelationDataService extends DataServiceFlatLeaf<IPpsEventTypeRelEntity, IMaterialNewEntity, PpsMaterialComplete> {

	// private parentService: PpsMaterialRecordDataService;

	public constructor(parentService: PpsMaterialRecordDataService) {
		const options: IDataServiceOptions<IPpsEventTypeRelEntity> = {
			apiUrl: 'productionplanning/ppsmaterial/ppseventtyperel',
			readInfo: <IDataServiceEndPointOptions>{
				endPoint: 'list',
				usePost: false,
			},
			roleInfo: <IDataServiceChildRoleOptions<IPpsEventTypeRelEntity, IMaterialNewEntity, PpsMaterialComplete>>{
				role: ServiceRole.Leaf,
				itemName: 'PpsEventTypeRel',
				parent: parentService
			}
		};

		super(options);
		// this.parentService = parentService;
	}

	protected override provideCreatePayload(): object {
		return {
			Id: this.getSelectedParent()?.Id
		};
	}

	protected override onCreateSucceeded(created: IPpsEventTypeRelEntity): IPpsEventTypeRelEntity {
		return created;
	}

	protected override provideLoadPayload(): object {
		return {
			mainItemId: this.getSelectedParent()?.Id
		};
	}

	protected override onLoadSucceeded(loaded: object): IPpsEventTypeRelEntity[] {
		return loaded as IPpsEventTypeRelEntity[];
	}

	public override isParentFn(parentKey: IMaterialNewEntity, entity: IPpsEventTypeRelEntity): boolean {
		return entity.MaterialFk === parentKey?.Id;
	}

	public override registerByMethod(): boolean {
		return true;
	}

	public override registerModificationsToParentUpdate(parentUpdate: PpsMaterialComplete, modified: IPpsEventTypeRelEntity[], deleted: IPpsEventTypeRelEntity[]): void {
		if (modified && modified.length > 0) {
			parentUpdate.PpsEventTypeRelToSave = modified;
		}

		if (deleted && deleted.length > 0) {
			parentUpdate.PpsEventTypeRelToDelete = deleted;
		}
	}

	public override getSavedEntitiesFromUpdate(parentUpdate: PpsMaterialComplete): IPpsEventTypeRelEntity[] {
		if (parentUpdate && !isNull(parentUpdate.PpsEventTypeRelToSave)) {
			return parentUpdate.PpsEventTypeRelToSave!;
		}
		return [];
	}
}