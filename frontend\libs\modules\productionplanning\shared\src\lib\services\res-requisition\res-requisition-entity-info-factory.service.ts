import { runInInjectionContext } from '@angular/core';
import { IEntityIdentification, IInitializationContext } from '@libs/platform/common';
import { IRequisitionEntity, RESOURCE_REQUISITION_LAYOUT_SERVICE_TOKEN } from '@libs/resource/interfaces';
import { EntityInfo } from '@libs/ui/business-base';
import { UiCommonDialogService } from '@libs/ui/common';
import { IPpsEntityInfoOptions } from '../../model';
import { PpsSharedResRequisitionDataServiceManager } from './res-requisition-data-service-manager.service';
import { PpsSharedResRequisitionBehavior } from './res-requisition-grid-behavior.service';
import { PpsSharedResRequisitionValidationService } from './res-requisition-validation.service';

export class PpsSharedResRequisitionEntityInfoFactory {
	public static create<PT extends IEntityIdentification>(options: IPpsEntityInfoOptions<PT>): EntityInfo {

		const getDataServ = (ctx: IInitializationContext) => PpsSharedResRequisitionDataServiceManager.getDataService<PT>(options, ctx);

		return EntityInfo.create<IRequisitionEntity>({
			grid: {
				title: options.gridTitle,
				containerUuid: options.containerUuid,
				behavior: ctx => new PpsSharedResRequisitionBehavior(ctx.injector.get(UiCommonDialogService), options.parentServiceFn(ctx), getDataServ(ctx)),
			},
			dataService: ctx => getDataServ(ctx),
			validationService: ctx => runInInjectionContext(ctx.injector, () =>
				new PpsSharedResRequisitionValidationService(getDataServ(ctx))
			),
			dtoSchemeId: { moduleSubModule: 'Resource.Requisition', typeName: 'RequisitionDto' },
			layoutConfiguration: async (ctx) => {
				const requisitionLayoutService = await ctx.lazyInjector.inject(RESOURCE_REQUISITION_LAYOUT_SERVICE_TOKEN);
				return requisitionLayoutService.generateLayout();
			},
			permissionUuid: options.permissionUuid,
			prepareEntityContainer: async (ctx) => {
				await Promise.all([
					ctx.translateService.load(['controlling.structure', 'basics.company', 'basics.material', 'basics.requisition',]),
					// other promises...
				]);
			},

		});


	}

}