/*
 * Copyright(c) RIB Software GmbH
 */

import { Injectable } from '@angular/core';

import { DataServiceFlatLeaf, IDataServiceOptions, ServiceRole, IDataServiceChildRoleOptions, IDataServiceEndPointOptions } from '@libs/platform/data-access';
import { IReport2CostCodeEntity, IReportEntity, ReportComplete } from '../model/models';
import { ProductionplanningReportReportDataService } from './productionplanning-report-report-data.service';
import { IIdentificationData } from '@libs/platform/common';
import { get } from 'lodash';

@Injectable({
	providedIn: 'root',
})
export class ProductionplanningReportCostCodeDataService extends DataServiceFlatLeaf<IReport2CostCodeEntity, IReportEntity, ReportComplete> {
	public constructor(parentDataService: ProductionplanningReportReportDataService) {
		const options: IDataServiceOptions<IReport2CostCodeEntity> = {
			apiUrl: 'productionplanning/report/report2costcode',
			readInfo: <IDataServiceEndPointOptions>{
				endPoint: 'list',
				prepareParam: (ident: IIdentificationData) => {
					return { ReportFk: ident.pKey1 };
				},
				usePost: false,
			},
			updateInfo: <IDataServiceEndPointOptions>{
				endPoint: 'update',
			},
			roleInfo: <IDataServiceChildRoleOptions<IReport2CostCodeEntity, IReportEntity, ReportComplete>>{
				role: ServiceRole.Leaf,
				itemName: 'Report2CostCode',
				parent: parentDataService,
				parentFilter: 'ReportFk',
			},
		};

		super(options);
	}
	/**
	 * Indicates that this service should be registered by method.
	 * @returns {boolean} Always returns true.
	 */
	public override registerByMethod(): boolean {
		return true;
	}

	/**
	 * Registers modifications (added/updated/deleted) to the parent update object.
	 * @param parentUpdate The parent ReportComplete object to update.
	 * @param modified Array of modified IReport2CostCodeEntity objects.
	 * @param deleted Array of deleted IReport2CostCodeEntity objects.
	 */
	public override registerModificationsToParentUpdate(parentUpdate: ReportComplete, modified: IReport2CostCodeEntity[], deleted: IReport2CostCodeEntity[]) {
		if (modified && modified.length > 0) {
			parentUpdate.Report2CostCodeToSave = modified;
		}

		if (deleted && deleted.length > 0) {
			parentUpdate.Report2CostCodeToDelete = deleted;
		}
	}

	/**
	 * Determines if the given entity belongs to the specified parent.
	 * @param parentKey The parent ReportComplete object.
	 * @param entity The IReport2CostCodeEntity to check.
	 * @returns {boolean} Always returns true.
	 */
	public override isParentFn(parentKey: IReportEntity, entity: IReport2CostCodeEntity): boolean {
		return true;
	}

	/**
	 * Retrieves the saved entities from the update object.
	 * @param complete The ReportComplete object containing saved entities.
	 * @returns {IReport2CostCodeEntity[]} Array of saved IReport2CostCodeEntity objects.
	 */
	public override getSavedEntitiesFromUpdate(complete: ReportComplete): IReport2CostCodeEntity[] {
		if (complete && complete.Report2CostCodeToSave) {
			return complete.Report2CostCodeToSave;
		}
		return [];
	}

	/**
	 * Provides the payload for loading data, based on the selected parent.
	 * @returns {object} The payload object with ReportFk or a default value.
	 */
	protected override provideLoadPayload(): object {
		const parentSelection = this.getSelectedParent();
		if (parentSelection) {
			return {
				ReportFk: parentSelection.Id,
			};
		}
		return {
			mainItemId: -1,
		};
	}

	/**
	 * Processes the loaded data and returns an array of IReport2CostCodeEntity.
	 * @param loaded The loaded data object from the backend.
	 * @returns {IReport2CostCodeEntity[]} Array of loaded IReport2CostCodeEntity objects.
	 */
	protected override onLoadSucceeded(loaded: object): IReport2CostCodeEntity[] {
		if (loaded) {
			return get(loaded, 'Main', []);
		}
		return [];
	}
}
