/*
 * Copyright(c) RIB Software GmbH
 */

import { EntityInfo } from '@libs/ui/business-base';
import { ProductionplanningReportTimeSheetDataService } from '../services/productionplanning-report-time-sheet-data.service';
import { ProductionplanningReportTimeSheetBehavior } from '../behaviors/productionplanning-report-time-sheet-behavior.service';
import { ITimeSheetEntity } from './models';
import { prefixAllTranslationKeys } from '@libs/platform/common';
import { PpsReportTimeSheetValidationService } from '../services/validations/pps-report-timesheet-validation.service';
import { BasicsSharedLookupOverloadProvider } from '@libs/basics/shared';

export const PRODUCTIONPLANNING_REPORT_TIME_SHEET_ENTITY_INFO: EntityInfo = EntityInfo.create<ITimeSheetEntity>({
	grid: {
		title: { key: 'productionplanning.report.timesheet.listTitle' },
		behavior: (ctx) => ctx.injector.get(ProductionplanningReportTimeSheetBehavior),
	},
	form: {
		title: { key: 'productionplanning.report.timesheet.detailTitle' },
		containerUuid: '4578ea4af68c4127abfed1fa84dabe67',
	},

	dataService: (ctx) => ctx.injector.get(ProductionplanningReportTimeSheetDataService),
	validationService:(ctx) => ctx.injector.get(PpsReportTimeSheetValidationService),
	dtoSchemeId: { moduleSubModule: 'ProductionPlanning.Report', typeName: 'TimeSheetDto' },
	permissionUuid: '5b8696a27b61436ba25137be3c5351e0',
	layoutConfiguration: async (context) => {
		return {
			groups: [
				{
					gid: 'baseGroup',
					attributes: ['ResourceFk', 'CountryFk', 'Description', 'Date', 'StartTime', 'EndTime'],
				},
				{
					gid: 'additionalInformation',
					attributes: ['HadBreak', 'BreakTime', 'Vacation', 'Sick', 'TimeOff', 'OverNight', 'Driver', 'Leader', 'Doctor', 'CommentText'],
				},
			],
			overloads: {
				CountryFk: BasicsSharedLookupOverloadProvider.provideCountryLookupOverload(true)
			},
			labels: {
				...prefixAllTranslationKeys('resource.master.', {
					ResourceFk: { key: 'entityResource' },
				}),
				...prefixAllTranslationKeys('basics.country.', {
					CountryFk: { key: 'entityCountryFk' },
				}),
				...prefixAllTranslationKeys('productionplanning.report.', {
					StartTime: { key: 'report.StartTime' },
					EndTime: { key: 'report.EndTime' },
					HadBreak: { key: 'timesheet.HadBreak' },
					BreakTime: { key: 'timesheet.BreakTime' },
					Vacation: { key: 'timesheet.Vacation' },
					Sick: { key: 'timesheet.Sick' },
					TimeOff: { key: 'timesheet.TimeOff' },
					OverNight: { key: 'timesheet.OverNight' },
					Driver: { key: 'timesheet.Driver' },
					Leader: { key: 'timesheet.Leader' },
					Doctor: { key: 'timesheet.Doctor' },
					additionalInformation: { key: 'timesheet.AddInfo' },
				}),
				...prefixAllTranslationKeys('cloud.common.', {
					Description: { key: 'entityDescription' },
					Date: { key: 'entityDate' },
					CommentText: { key: 'entityComment' },
				}),
			},
		};
	},
});
