import { inject, Injectable } from '@angular/core';
import { IIdentificationData } from '@libs/platform/common';
import { FieldType, ILookupSearchRequest, ILookupSearchResponse, UiCommonLookupReadonlyDataService } from '@libs/ui/common';
import { Observable } from 'rxjs';
import { IMaterialNewEntity } from '../model/models';
import { PpsMaterialRecordDataService } from './material/material-record-data.service';

@Injectable({
	providedIn: 'root'
})
export class MaterialNewLookupService extends UiCommonLookupReadonlyDataService<IMaterialNewEntity> {
	private readonly dataService = inject(PpsMaterialRecordDataService);

	public constructor() {
		super({
			uuid: '3e099a7d50044b8a85ea1cbbd72713c7',
			valueMember: 'Id',
			displayMember: 'Code',
			gridConfig: {
				columns: [
					{
						id: 'code',
						model: 'Code',
						label: {
							key: 'cloud.common.entityCode'
						},
						type: FieldType.Code,
						sortable: true,
						visible: true
					},

				]
			},

			isClientSearch: true,
			canListAll: true
		});
	}

	public getItemByKey(key: IIdentificationData): Observable<IMaterialNewEntity> {
		return new Observable<IMaterialNewEntity>(e => {
			e.next(this.dataService.getList().find(e => e.Id === key.id));
			e.complete();
		});
	}

	public getList(): Observable<IMaterialNewEntity[]> {
		return new Observable<IMaterialNewEntity[]>(e => {
			e.next(this.dataService.getList());
			e.complete();
		});
	}

	public getSearchList(request: ILookupSearchRequest): Observable<ILookupSearchResponse<IMaterialNewEntity>> {
		throw new Error('Using client side search, this method should not be called');
	}
}