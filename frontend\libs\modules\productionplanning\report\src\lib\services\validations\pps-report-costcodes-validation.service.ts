/*
 * Copyright(c) RIB Software GmbH
 */
import { BaseValidationService, IEntityRuntimeDataRegistry, IValidationFunctions } from '@libs/platform/data-access';
import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { IReport2CostCodeEntity } from '../../model/models';
import { ProductionplanningReportCostCodeDataService } from '../productionplanning-report-cost-code-data.service';

@Injectable({
    providedIn: 'root',
})
export class PpsReportCostcodesValidationService extends BaseValidationService<IReport2CostCodeEntity> {
    private  readonly dataService = inject(ProductionplanningReportCostCodeDataService);
    protected readonly http = inject(HttpClient);

    protected generateValidationFunctions(): IValidationFunctions<IReport2CostCodeEntity> {
        return {
            CostCodeFk: this.validateIsMandatory,
            UomFk: this.validateIsMandatory,
        };
    }
    protected getEntityRuntimeData(): IEntityRuntimeDataRegistry<IReport2CostCodeEntity> {
        return this.dataService;
    }
}
