/*
 * Copyright(c) RIB Software GmbH
 */
import { Injectable } from '@angular/core';
import {
	BasicsSharedCustomizeLookupOverloadProvider,
	BasicsSharedUomLookupService,
} from '@libs/basics/shared';
import { prefixAllTranslationKeys } from '@libs/platform/common';
import { IPPSEventEntity, PpsItemLookupService, PpsProcessCommonLookupService, ProductionplanningSharedPpsEventLookupService } from '@libs/productionplanning/shared';
import { ConcreteFieldOverload, createLookup, FieldType, ILayoutConfiguration, ILookupContext } from '@libs/ui/common';
import { IPpsUpstreamItemEntity } from '../../model/entities/pps-upstream-item-entity.interface';
import { IPPSItemEntity} from '@libs/productionplanning/common';
import { PpsUpstreamTypes } from '../../model/constants/pps-upsteam-types';
import { BehaviorSubject } from 'rxjs';
import { PpsUpstreamItemPesItemLookupService } from './pps-upstream-item-pes-item-lookup.service';
import { IPesItemPpsUpstrmIdEntity } from '../../model/entities/pes-item-pps-upstrmid.interface';

/**
 * PPS UpstreamItem layout service
 */
@Injectable({
	providedIn: 'root'
})
export class PpsUpstreamItemLayoutService {

	private readonly upstreamResultSubject = new BehaviorSubject<ConcreteFieldOverload<IPpsUpstreamItemEntity>>({
		type: FieldType.Description
	});

	private updateUpstreamResultOverload(entity?: IPpsUpstreamItemEntity) {
		let value = {};
		if (entity && entity.PpsUpstreamTypeFk) {
			switch (entity?.PpsUpstreamTypeFk) {

				case PpsUpstreamTypes.Production: // 1
					value = {
						type: FieldType.Lookup,
						lookupOptions: createLookup<IPpsUpstreamItemEntity, IPPSItemEntity>({
							dataServiceToken: PpsItemLookupService, // pu lookup
							showClearButton: true
						})
					};
					break;
				// case PpsUpstreamTypes.Acquisition: // 2
				// 	value = {
				// 		type: FieldType.Lookup,
				// 		lookupOptions: createLookup<IPpsUpstreamItemEntity, ?>({
				// 			dataServiceToken: ?
				// 			showClearButton: true
				// 		})
				// 	};
				// 	break;

				// TODO(Resource Requisition Lookup) - waiting for https://rib-40.atlassian.net/browse/DEV-39105
				// case PpsUpstreamTypes.FromStock: // 3
				// 	value = {
				// 		type: FieldType.Lookup,
				// 		lookupOptions: createLookup<IPpsUpstreamItemEntity, ?>({
				// 			dataServiceToken: ?
				// 			showClearButton: true
				// 		})
				// 	};
				// 	break;

				case PpsUpstreamTypes.SuppliedByCustomer: // 4
					value = {
						type: FieldType.Lookup,
						lookupOptions: createLookup<IPpsUpstreamItemEntity, IPesItemPpsUpstrmIdEntity>({
							dataServiceToken: PpsUpstreamItemPesItemLookupService,
							showClearButton: true,
							serverSideFilter: {
								key: 'unassigned-upstream-filter',
								execute(context: ILookupContext<IPesItemPpsUpstrmIdEntity, IPpsUpstreamItemEntity>) {
									return {
										PrjProjectFk: context?.entity?.ProjectFk,
									};
								}
							}
						})
					};
					break;
				// TODO(Formwork Lookup) - waiting for implementation of Formwork Lookup from Formwork module? 
				// case PpsUpstreamTypes.Formwork: // 6
				// 	value = {
				// 		type: FieldType.Lookup,
				// 		lookupOptions: createLookup<IPpsUpstreamItemEntity, ?>({
				// 			dataServiceToken: ?,
				// 			showClearButton: true
				// 		})
				// 	};
				// 	break;
				case PpsUpstreamTypes.Process: // 7
					value = {
						type: FieldType.Lookup,
						lookupOptions: createLookup({
							showDialog: true,
							showClearButton: true,
							dataServiceToken: PpsProcessCommonLookupService,
						})
					};
					break;		

				default:
					value = {};
			}

			this.upstreamResultSubject.next(value);
		}
	}

	public generateLayout(): ILayoutConfiguration<IPpsUpstreamItemEntity> {
		return <ILayoutConfiguration<IPpsUpstreamItemEntity>>{
			groups: [
				{
					gid: 'baseGroup',
					attributes: ['PpsItemFk', 'PpsUpstreamStatusFk', 'PpsUpstreamTypeFk', 'UpstreamResult', 'UpstreamResultStatus',
						'PpsUpstreamGoodsTypeFk', 'UpstreamGoods', 'Quantity', 'UomFk', 'Comment', 'PpsEventReqforFk', 'AvailableQuantity',
						'OpenQuantity', 'SplitQuantity', 'RemainingQuantity', 'PpsEventtypeReqforFk', 'IsForTransport', 'IsImported',
						'TrsOpenQuantity', 'TrsAssignedQuantity', 'EngDrawingFk']
				},
				{
					gid: 'planningGroup',
					attributes: ['DueDate']
				},
				{
					gid: 'userDefTextGroup',
					attributes: ['Userdefined1', 'Userdefined2', 'Userdefined3', 'Userdefined4', 'Userdefined5']
				},
				{
					gid: 'userDefDateGroup',
					attributes: ['UserDefinedDate1', 'UserDefinedDate2', 'UserDefinedDate3', 'UserDefinedDate4', 'UserDefinedDate5']
				},
				{
					gid: 'userDefDateTimeGroup',
					attributes: ['UserDefinedDateTime1', 'UserDefinedDateTime2', 'UserDefinedDateTime3', 'UserDefinedDateTime4', 'UserDefinedDateTime5']
				},
			],
			overloads: {
				PpsItemFk: {
					// navigator
					type: FieldType.Lookup,
					lookupOptions: createLookup<IPpsUpstreamItemEntity, IPPSItemEntity>({
						dataServiceToken: PpsItemLookupService,
						showClearButton: true
					})
				},
				PpsEventReqforFk: {
					type: FieldType.Lookup,
					lookupOptions: createLookup<IPpsUpstreamItemEntity, IPPSEventEntity>({
						dataServiceToken: ProductionplanningSharedPpsEventLookupService,
						showClearButton: true,
						clientSideFilter: {
							execute(item: IPPSEventEntity, context: ILookupContext<IPPSEventEntity, IPpsUpstreamItemEntity>): boolean {
								return (item.ItemFK === context?.entity?.PpsItemFk);
							}
						}
					})
				},
				PpsUpstreamStatusFk: BasicsSharedCustomizeLookupOverloadProvider.providePpsUpstreamItemStatusReadonlyLookupOverload(),
				PpsUpstreamGoodsTypeFk: BasicsSharedCustomizeLookupOverloadProvider.providePpsUpstreamGoodsTypeReadonlyLookupOverload(),
				PpsUpstreamTypeFk: BasicsSharedCustomizeLookupOverloadProvider.providePpsUpstreamTypeReadonlyLookupOverload(),
				UpstreamGoods: {
					// todo
				},
				UpstreamResult: {
					type: FieldType.Dynamic,
					overload: ctx => {
						this.updateUpstreamResultOverload(ctx.entity);
						return this.upstreamResultSubject;
					},
				},
				UomFk: {
					type: FieldType.Lookup,
					lookupOptions: createLookup({
						dataServiceToken: BasicsSharedUomLookupService,
						showClearButton: true,
					})
				},
			},
			labels: {
				...prefixAllTranslationKeys('cloud.common.', {
					PpsUpstreamStatusFk: 'entityStatus',
					Userdefined1: { key: 'entityUserDefined', params: { p_0: '1' } },
					Userdefined2: { key: 'entityUserDefined', params: { p_0: '2' } },
					Userdefined3: { key: 'entityUserDefined', params: { p_0: '3' } },
					Userdefined4: { key: 'entityUserDefined', params: { p_0: '4' } },
					Userdefined5: { key: 'entityUserDefined', params: { p_0: '5' } },
					UserDefinedDate1: { key: 'entityUserDefinedDate', params: { p_0: '1' } },
					UserDefinedDate2: { key: 'entityUserDefinedDate', params: { p_0: '2' } },
					UserDefinedDate3: { key: 'entityUserDefinedDate', params: { p_0: '3' } },
					UserDefinedDate4: { key: 'entityUserDefinedDate', params: { p_0: '4' } },
					UserDefinedDate5: { key: 'entityUserDefinedDate', params: { p_0: '5' } }
				}),
				...prefixAllTranslationKeys('productionplanning.common.', {
					EngDrawingFk: 'product.drawing',
				}),
				...prefixAllTranslationKeys('productionplanning.item.', {
					PpsItemFk: 'entityItem',
					Quantity: 'quantity',
					UomFk: 'uomFk',
					userDefDateTimeGroup: 'userDefDateTimeGroup',
					UserDefinedDateTime1: { key: 'entityUserDefinedDateTime', params: { p_0: '1' } },
					UserDefinedDateTime2: { key: 'entityUserDefinedDateTime', params: { p_0: '2' } },
					UserDefinedDateTime3: { key: 'entityUserDefinedDateTime', params: { p_0: '3' } },
					UserDefinedDateTime4: { key: 'entityUserDefinedDateTime', params: { p_0: '4' } },
					UserDefinedDateTime5: { key: 'entityUserDefinedDateTime', params: { p_0: '5' } },
				}),
				...prefixAllTranslationKeys('productionplanning.item.upstreamItem.', {
					PpsUpstreamTypeFk: 'ppsUpstreamTypeFk',
					PpsEventReqforFk: 'ppseventreqfor',
					PpsUpstreamGoodsTypeFk: 'ppsupstreamgoodstype',
					UpstreamResult: 'upstreamresult',
					UpstreamGoods: 'upstreamgoods',
					UpstreamResultStatus: 'upstreamresultstatus',
					AvailableQuantity: 'availableQuantity',
					OpenQuantity: 'openQuantity',
					PpsUpstreamItemFk: 'ppsUpstreamItemFk',
					SplitQuantity: 'splitQuantity',
					RemainingQuantity: 'remainingQuantity',
					PpsEventtypeReqforFk: 'ppsEventtypeReqforFk',
					UpstreamItemQuantity: 'upstreamItemQuantity',
					IsForTransport: 'isForTransport',
					IsImported: 'isImported',
					TrsOpenQuantity: 'TrsOpenQuantity',
					TrsAssignedQuantity: 'TrsAssignedQuantity',
					planningGroup: 'planningGroup',
					DueDate: 'dueDate',
				}),

			},
		};
	}
}