/*
 * Copyright(c) RIB Software GmbH
 */

import { IEntityProcessor, IReadOnlyField } from '@libs/platform/data-access';
import { ProductionplanningReportTimeSheetDataService } from '../productionplanning-report-time-sheet-data.service';
import { Injectable } from '@angular/core';
import { ITimeSheetEntity } from '../../model/models';

@Injectable({
	providedIn: 'root',
})
export class PpsReportTimesheetProcessorService<T extends ITimeSheetEntity> implements IEntityProcessor<T> {
	private dataService!: ProductionplanningReportTimeSheetDataService;

	/**
	 * Dynamically set the data service to avoid circular dependency.
	 */
	public setDataService(dataService: ProductionplanningReportTimeSheetDataService): void {
		this.dataService = dataService;
	}

	public process(item: ITimeSheetEntity): void {
		if (!item) {
			return;
		}
		const readonlyFields: IReadOnlyField<ITimeSheetEntity>[] = [
			{
				field: 'ResourceFk',
				readOnly: true,
			},
			{
				field: 'CountryFk',
				readOnly: true,
			},
			{
				field: 'Description',
				readOnly: true,
			},
			{
				field: 'Date',
				readOnly: true,
			},
			{
				field: 'StartTime',
				readOnly: true,
			},
			{
				field: 'EndTime',
				readOnly: true,
			},
			{
				field: 'HadBreak',
				readOnly: true,
			},
			{
				field: 'BreakTime',
				readOnly: true,
			},
			{
				field: 'Vacation',
				readOnly: true,
			},
			{
				field: 'Sick',
				readOnly: true,
			},
			{
				field: 'TimeOff',
				readOnly: true,
			},
			{
				field: 'OverNight',
				readOnly: true,
			},
			{
				field: 'Driver',
				readOnly: true,
			},
			{
				field: 'Leader',
				readOnly: true,
			},
			{
				field: 'Doctor',
				readOnly: true,
			},
			{
				field: 'CommentText',
				readOnly: true,
			},
		];
		this.dataService.setEntityReadOnlyFields(item, readonlyFields);
	}
	/**
	 * Reverts any processing changes made to production planning entities.
	 */
	public revertProcess(): void {}
}
