/*
 * Copyright(c) RIB Software GmbH
 */

import { inject, Injectable, InjectionToken } from '@angular/core';
import { BaseValidationService, IEntityRuntimeDataRegistry, IValidationFunctions } from '@libs/platform/data-access';
import { IPpsEventTypeRelEntity } from '../../model/models';
import { PpsEventTypeRelationDataService } from './pps-event-type-relation-data.service';

export const PPS_CAD_TO_MATERIAL_VALIDATION_TOKEN = new InjectionToken<PpsEventTypeRelationValidationService>('ppsCadToMaterialValidationToken');

@Injectable({
	providedIn: 'root'
})
export class PpsEventTypeRelationValidationService extends BaseValidationService<IPpsEventTypeRelEntity> {

	private dataService = inject(PpsEventTypeRelationDataService);

	protected generateValidationFunctions(): IValidationFunctions<IPpsEventTypeRelEntity> {
		return {
			PpsEventTypeParentFk: this.validateIsMandatory,
			PpsEventTypeChildFk: this.validateIsMandatory,
			RelationKindFk: this.validateIsMandatory,
		};
	}

	protected getEntityRuntimeData(): IEntityRuntimeDataRegistry<IPpsEventTypeRelEntity> {
		return this.dataService;
	}

	// private validateMdcMaterialFk(info: ValidationInfo<IPpsEventTypeRelEntity>): ValidationResult {
	// 	return this.validateIsMandatory(info);
	// }
}
