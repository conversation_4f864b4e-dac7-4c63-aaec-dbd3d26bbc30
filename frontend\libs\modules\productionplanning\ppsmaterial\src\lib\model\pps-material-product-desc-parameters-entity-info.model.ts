/*
 * Copyright(c) RIB Software GmbH
 */

import { EntityInfo } from '@libs/ui/business-base';
import { IMdcProductDescParamEntity } from './models';
import { ILayoutConfiguration } from '@libs/ui/common';
import { prefixAllTranslationKeys } from '@libs/platform/common';
import { BasicsSharedLookupOverloadProvider } from '@libs/basics/shared';
import { PpsMaterialProductDescParametersDataService } from '../services/product-description/pps-material-product-desc-parameters-data.service';

export const PPS_MATERIAL_PRODUCT_DESC_PARAMETERS_ENTITY_INFO: EntityInfo = EntityInfo.create<IMdcProductDescParamEntity>({
	grid: {
		title: { key: 'productionplanning.ppsmaterial.productDescParameter.listViewTitle' },
		containerUuid: '3a0ff92d9fc74bc691845427bf566bd3',
	},
	dataService: (ctx) => ctx.injector.get(PpsMaterialProductDescParametersDataService),
	dtoSchemeId: { moduleSubModule: 'ProductionPlanning.PpsMaterial', typeName: 'MdcProductDescParamDto' },
	permissionUuid: '3a0ff92d9fc74bc691845427bf566bd3',
	layoutConfiguration: async (context) => {
		return <ILayoutConfiguration<IMdcProductDescParamEntity>>{
			groups: [
				{
					gid: 'basicData',
					title: {
						key: 'cloud.common.listProductDescTitle',
						text: 'Basic Data',
					},
					attributes: ['DescriptionInfo', 'Quantity', 'UoMFk', 'Sorting', 'UomFk', 'VariableName'],
				},
			],
			overloads: {
				UomFk: BasicsSharedLookupOverloadProvider.provideUoMLookupOverload(false),
			},
			labels: {
				...prefixAllTranslationKeys('cloud.common.', {
					DescriptionInfo: { key: 'entityDescription', text: 'Description' },
					UomFk: { key: 'entityUoM', text: 'UoM' },
					Quantity: { key: 'entityQuantity', text: 'Quantity' },
					UoMFk: { key: 'entityUoM', text: 'UoM' },
					Sorting: { key: 'entitySorting', text: 'Sorting' },
				}),
				...prefixAllTranslationKeys('productionplanning.ppsmaterial.', {
					VariableName: { key: 'productDescParameter.variablename', text: '*Variable Name' },
				}),
			},
		};
	},
});
